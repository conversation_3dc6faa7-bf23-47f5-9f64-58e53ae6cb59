# Lahza Payment Gateway - Automated Setup Script
# PowerShell script to automate ngrok setup and testing

param(
    [string]$AuthToken = "",
    [int]$Port = 80,
    [switch]$SkipChecks = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Lahza Payment Gateway - Auto Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if a command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Function to check if port is listening
function Test-Port {
    param($Port)
    try {
        $listener = Get-NetTCPConnection -LocalPort $Port -State Listen -ErrorAction SilentlyContinue
        return $listener -ne $null
    } catch {
        return $false
    }
}

# Function to get ngrok URL
function Get-NgrokUrl {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:4040/api/tunnels" -Method Get -ErrorAction Stop
        $httpsUrl = $response.tunnels | Where-Object { $_.proto -eq "https" } | Select-Object -First 1
        if ($httpsUrl) {
            return $httpsUrl.public_url
        }
        return $null
    } catch {
        return $null
    }
}

# Step 1: Check prerequisites
if (-not $SkipChecks) {
    Write-Host "[INFO] Checking prerequisites..." -ForegroundColor Yellow
    
    # Check ngrok
    if (-not (Test-Command "ngrok")) {
        Write-Host "[ERROR] ngrok is not installed or not in PATH" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please install ngrok:" -ForegroundColor Yellow
        Write-Host "1. Download from: https://ngrok.com/download" -ForegroundColor White
        Write-Host "2. Or install via Chocolatey: choco install ngrok" -ForegroundColor White
        Write-Host "3. Or install via Scoop: scoop install ngrok" -ForegroundColor White
        exit 1
    }
    Write-Host "[SUCCESS] ngrok found" -ForegroundColor Green
    
    # Check XAMPP
    if (-not (Test-Port $Port)) {
        Write-Host "[WARNING] Port $Port is not listening" -ForegroundColor Yellow
        Write-Host "Starting XAMPP Control Panel..." -ForegroundColor Yellow
        
        $xamppPath = "C:\xampp\xampp-control.exe"
        if (Test-Path $xamppPath) {
            Start-Process $xamppPath
            Write-Host "Please start Apache and MySQL, then press Enter to continue..." -ForegroundColor Yellow
            Read-Host
        } else {
            Write-Host "[ERROR] XAMPP not found at $xamppPath" -ForegroundColor Red
            Write-Host "Please start your web server manually and ensure port $Port is listening" -ForegroundColor Yellow
            Read-Host "Press Enter when ready..."
        }
    }
    
    if (Test-Port $Port) {
        Write-Host "[SUCCESS] Port $Port is listening" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Port $Port is still not available" -ForegroundColor Red
        exit 1
    }
}

# Step 2: Setup ngrok auth token
Write-Host ""
Write-Host "[INFO] Setting up ngrok authentication..." -ForegroundColor Yellow

if ($AuthToken -eq "") {
    Write-Host ""
    Write-Host "To get your auth token:" -ForegroundColor Cyan
    Write-Host "1. Go to: https://dashboard.ngrok.com/get-started/your-authtoken" -ForegroundColor White
    Write-Host "2. Sign up for a free account if needed" -ForegroundColor White
    Write-Host "3. Copy your auth token" -ForegroundColor White
    Write-Host ""
    
    # Open browser to ngrok dashboard
    Start-Process "https://dashboard.ngrok.com/get-started/your-authtoken"
    
    $AuthToken = Read-Host "Enter your ngrok auth token"
}

if ($AuthToken -eq "") {
    Write-Host "[ERROR] Auth token is required" -ForegroundColor Red
    exit 1
}

# Add auth token
Write-Host "[INFO] Adding auth token to ngrok..." -ForegroundColor Yellow
try {
    $result = & ngrok config add-authtoken $AuthToken 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[SUCCESS] Auth token added successfully" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Failed to add auth token: $result" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "[ERROR] Failed to add auth token: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Start ngrok tunnel
Write-Host ""
Write-Host "[INFO] Starting ngrok tunnel..." -ForegroundColor Yellow

# Start ngrok in background
$ngrokJob = Start-Job -ScriptBlock {
    param($Port)
    & ngrok http $Port
} -ArgumentList $Port

# Wait for ngrok to start
Write-Host "[INFO] Waiting for ngrok to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Get ngrok URL
$maxAttempts = 10
$attempt = 0
$ngrokUrl = $null

do {
    $attempt++
    Write-Host "[INFO] Attempt $attempt to get ngrok URL..." -ForegroundColor Yellow
    $ngrokUrl = Get-NgrokUrl
    if ($ngrokUrl) {
        break
    }
    Start-Sleep -Seconds 2
} while ($attempt -lt $maxAttempts)

if ($ngrokUrl) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "   NGROK TUNNEL ACTIVE" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Public URL: $ngrokUrl" -ForegroundColor Cyan
    Write-Host "Local URL:  http://localhost:$Port" -ForegroundColor White
    Write-Host "Web Interface: http://localhost:4040" -ForegroundColor White
    Write-Host ""
    
    # Generate webhook URL
    $webhookUrl = "$ngrokUrl/Whmcs/modules/gateways/callback/lahza.php"
    Write-Host "Webhook URL for Lahza:" -ForegroundColor Yellow
    Write-Host $webhookUrl -ForegroundColor Cyan
    
    # Copy to clipboard
    try {
        $webhookUrl | Set-Clipboard
        Write-Host ""
        Write-Host "[SUCCESS] Webhook URL copied to clipboard!" -ForegroundColor Green
    } catch {
        Write-Host ""
        Write-Host "[INFO] Please copy the webhook URL manually" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "[ERROR] Could not get ngrok URL. Please check http://localhost:4040" -ForegroundColor Red
}

# Step 4: Open interfaces
Write-Host ""
Write-Host "[INFO] Opening interfaces..." -ForegroundColor Yellow

# Open ngrok web interface
Start-Process "http://localhost:4040"

# Open WHMCS test suite
$testSuiteUrl = if ($ngrokUrl) { "$ngrokUrl/Whmcs/final_test_suite.php" } else { "http://localhost/Whmcs/final_test_suite.php" }
Start-Sleep -Seconds 2
Start-Process $testSuiteUrl

# Step 5: Instructions
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   NEXT STEPS" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Update Lahza Dashboard:" -ForegroundColor Yellow
Write-Host "   - Go to your Lahza dashboard" -ForegroundColor White
Write-Host "   - Navigate to Webhook settings" -ForegroundColor White
Write-Host "   - Set webhook URL to:" -ForegroundColor White
Write-Host "     $webhookUrl" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. Test the Gateway:" -ForegroundColor Yellow
Write-Host "   - Create a new invoice in WHMCS" -ForegroundColor White
Write-Host "   - Set payment method to 'Lahza'" -ForegroundColor White
Write-Host "   - Process a test payment" -ForegroundColor White
Write-Host "   - Use test card: ****************" -ForegroundColor White
Write-Host ""
Write-Host "3. Monitor Activity:" -ForegroundColor Yellow
Write-Host "   - ngrok interface: http://localhost:4040" -ForegroundColor White
Write-Host "   - WHMCS Gateway Logs: Admin → Logs → Gateway Log" -ForegroundColor White
Write-Host ""

# Step 6: Keep running
Write-Host "========================================" -ForegroundColor Green
Write-Host "   TUNNEL IS RUNNING" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Keep this window open while testing!" -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop the tunnel when done." -ForegroundColor Yellow
Write-Host ""

# Monitor the tunnel
try {
    while ($true) {
        # Check if ngrok is still running
        if ($ngrokJob.State -ne "Running") {
            Write-Host "[ERROR] ngrok tunnel stopped unexpectedly" -ForegroundColor Red
            break
        }
        
        # Check tunnel status every 30 seconds
        Start-Sleep -Seconds 30
        
        $currentUrl = Get-NgrokUrl
        if ($currentUrl -ne $ngrokUrl) {
            if ($currentUrl) {
                Write-Host "[INFO] Tunnel URL changed to: $currentUrl" -ForegroundColor Yellow
                $ngrokUrl = $currentUrl
            } else {
                Write-Host "[WARNING] Tunnel URL not accessible" -ForegroundColor Yellow
            }
        }
    }
} catch {
    Write-Host ""
    Write-Host "[INFO] Tunnel stopped by user" -ForegroundColor Yellow
} finally {
    # Cleanup
    if ($ngrokJob) {
        Stop-Job $ngrokJob -Force
        Remove-Job $ngrokJob -Force
    }
    Write-Host "[INFO] Cleanup completed" -ForegroundColor Green
}
