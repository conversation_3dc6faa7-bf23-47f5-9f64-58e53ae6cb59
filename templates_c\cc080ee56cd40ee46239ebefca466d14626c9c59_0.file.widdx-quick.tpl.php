<?php
/* Smarty version 3.1.48, created on 2025-06-18 00:44:44
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\widget\widdx-quick.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_6851efdcc98342_58276353',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'cc080ee56cd40ee46239ebefca466d14626c9c59' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\widget\\widdx-quick.tpl',
      1 => 1738887214,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6851efdcc98342_58276353 (Smarty_Internal_Template $_smarty_tpl) {
?><!-- Quick Actions Modal -->
<?php $_smarty_tpl->_assignInScope('quickActions', array(array('title'=>'My Services','icon'=>'server','color'=>'primary','url'=>"clientarea.php?action=services",'description'=>'Manage your hosting services and control panels'),array('title'=>'My Domains','icon'=>'globe','color'=>'success','url'=>"clientarea.php?action=domains",'description'=>'Manage your domain names'),array('title'=>'Billing','icon'=>'file-invoice-dollar','color'=>'warning','url'=>"clientarea.php?action=invoices",'description'=>'View invoices and make payments'),array('title'=>'Support Tickets','icon'=>'ticket-alt','color'=>'info','url'=>"supporttickets.php",'description'=>'Get help from our support team'),array('title'=>'Email Services','icon'=>'envelope','color'=>'danger','url'=>"clientarea.php?action=emails",'description'=>'Manage your email accounts'),array('title'=>'SSL Certificates','icon'=>'shield-alt','color'=>'secondary','url'=>"clientarea.php?action=services&type=ssl",'description'=>'Manage SSL/TLS certificates'),array('title'=>'Downloads','icon'=>'download','color'=>'info','url'=>"clientarea.php?action=downloads",'description'=>'Access downloadable resources'),array('title'=>'Announcements','icon'=>'bullhorn','color'=>'warning','url'=>"clientarea.php?action=announcements",'description'=>'Stay updated with latest news'),array('title'=>'Affiliates','icon'=>'users','color'=>'success','url'=>"affiliates.php",'description'=>'Join our affiliate program'),array('title'=>'Network Status','icon'=>'signal','color'=>'primary','url'=>"serverstatus.php",'description'=>'View system status and uptime'),array('title'=>'SEO Tools','icon'=>'chart-line','color'=>'danger','url'=>((string)$_smarty_tpl->tpl_vars['WEB_ROOT']->value)."/widdx-page.php?page=seo-analyzer",'description'=>'Analyze and improve SEO'),array('title'=>'Whois Lookup','icon'=>'search','color'=>'secondary','url'=>((string)$_smarty_tpl->tpl_vars['WEB_ROOT']->value)."/widdx-page.php?page=whois-checker",'description'=>'Check domain information')));?>

<div class="modal fade" id="quickActionsModal" tabindex="-1" aria-labelledby="quickActionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content border-0 shadow-lg">
      <div class="modal-header bg-white border-bottom">
        <h5 class="modal-title text-dark" id="quickActionsModalLabel">
          <i class="fas fa-bolt me-2 text-primary"></i>Quick Actions
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body py-4">
        <div class="row g-4">
          <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['quickActions']->value, 'action');
$_smarty_tpl->tpl_vars['action']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['action']->value) {
$_smarty_tpl->tpl_vars['action']->do_else = false;
?>
          <div class="col-6 col-md-4">
            <a href="<?php echo $_smarty_tpl->tpl_vars['action']->value['url'];?>
" class="text-decoration-none">
              <div class="card h-100 border-0 shadow-sm quick-action-card">
                <div class="card-body d-flex flex-column justify-content-center align-items-center p-4">
                  <i class="fas fa-<?php echo $_smarty_tpl->tpl_vars['action']->value['icon'];?>
 fa-2x text-<?php echo $_smarty_tpl->tpl_vars['action']->value['color'];?>
 mb-3"></i>
                  <h6 class="card-title mb-2 text-center"><?php echo $_smarty_tpl->tpl_vars['action']->value['title'];?>
</h6>
                  <p class="card-text small text-muted text-center mb-0"><?php echo $_smarty_tpl->tpl_vars['action']->value['description'];?>
</p>
                </div>
              </div>
            </a>
          </div>
          <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
#quickActionsModal .modal-content {
  border-radius: 15px;
  overflow: hidden;
}

#quickActionsModal .modal-header {
  padding: 1.5rem;
}

#quickActionsModal .quick-action-card {
  transition: all 0.3s ease;
  border-radius: 10px;
}

#quickActionsModal .quick-action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}

#quickActionsModal .quick-action-card i {
  transition: transform 0.3s ease;
}

#quickActionsModal .quick-action-card:hover i {
  transform: scale(1.1);
}

#quickActionsModal .card-title {
  font-weight: 600;
  color: #333;
  transition: color 0.3s ease;
}

#quickActionsModal .quick-action-card:hover .card-title {
  color: #007bff;
}

#quickActionsModal .card-text {
  font-size: 0.85rem;
  line-height: 1.4;
}
</style><?php }
}
