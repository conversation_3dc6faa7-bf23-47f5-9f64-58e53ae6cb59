# 🚀 دليلك الشخصي للبدء السريع
# Your Personal Quick Start Guide

## 🎯 كل شيء جاهز لك! | Everything Ready for You!

تم إعداد ملفات مخصصة لك مع ngrok token الخاص بك مسبقاً.

## 🔑 معلوماتك | Your Information
- **ngrok Auth Token**: `*************************************************` ✅
- **Token Status**: مُعد مسبقاً في الملفات

## 🚀 طرق التشغيل | Launch Methods

### الطريقة 1: تشغيل فوري (الأسهل)
```bash
# انقر نقرة مزدوجة على الملف
START_LAHZA_WITH_TOKEN.bat
```
**المميزات:**
- ✅ Token مُعد مسبقاً
- ✅ تشغيل تلقائي كامل
- ✅ فتح جميع الواجهات
- ✅ تعليمات واضحة

### الطريقة 2: PowerShell متقدم
```powershell
# تشغيل PowerShell كمدير
.\Start-LahzaWithToken.ps1
```
**المميزات:**
- ✅ مراقبة متقدمة
- ✅ إدارة أخطاء ذكية
- ✅ تحديثات حالة مستمرة

## 📋 ما سيحدث تلقائياً | What Happens Automatically

### 1. فحص النظام | System Check
- ✅ فحص XAMPP وتشغيله
- ✅ فحص ngrok
- ✅ إعداد token تلقائياً

### 2. بدء الخدمات | Start Services
- ✅ تشغيل ngrok tunnel
- ✅ إنشاء URL عام
- ✅ فتح الواجهات

### 3. الواجهات المفتوحة | Opened Interfaces
- 🌐 **ngrok Interface**: http://localhost:4040
- 🔧 **Webhook Updater**: سيفتح تلقائياً
- 👨‍💼 **WHMCS Admin**: سيفتح تلقائياً
- 🧪 **Test Suite**: سيفتح تلقائياً
- 🏢 **Lahza Dashboard**: https://dashboard.lahza.io

## 📝 خطوات ما بعد التشغيل | Post-Launch Steps

### الخطوة 1: نسخ Webhook URL
1. انظر إلى تبويب "Lahza Webhook Updater"
2. انسخ URL الذي يبدأ بـ `https://`
3. سيكون مثل: `https://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php`

### الخطوة 2: تحديث لوحة تحكم لحظة
1. اذهب إلى: https://dashboard.lahza.io
2. سجل الدخول بحسابك
3. ابحث عن إعدادات "Webhook" أو "API"
4. الصق URL من الخطوة 1
5. فعّل الأحداث:
   - ✅ `charge.success`
   - ✅ `charge.failed`
6. احفظ الإعدادات

### الخطوة 3: اختبار الدفع
1. **إنشاء فاتورة جديدة**:
   - WHMCS Admin → Billing → Create Invoice
   - اختر عميل أو أنشئ جديد
   - أضف منتج/خدمة
   - **مهم**: اختر Payment Method = "Lahza"

2. **معالجة الدفع**:
   - اذهب للفاتورة من جهة العميل
   - اضغط "Pay Now"
   - استخدم بطاقة الاختبار:
     - **رقم البطاقة**: `****************`
     - **CVV**: `004`
     - **تاريخ الانتهاء**: `03/30`

3. **التحقق من النجاح**:
   - ✅ حالة الفاتورة تصبح "Paid"
   - ✅ webhook يظهر في ngrok interface
   - ✅ سجل الدفع يظهر في WHMCS

## 🔍 مراقبة النظام | System Monitoring

### أثناء الاختبار | During Testing
- **ngrok Interface**: http://localhost:4040
  - راقب webhook requests من لحظة
  - تحقق من response codes

- **WHMCS Gateway Logs**: Admin → Logs → Gateway Log
  - ابحث عن "lahza"
  - تحقق من رسائل النجاح/الخطأ

### علامات النجاح | Success Indicators
- ✅ ngrok tunnel نشط
- ✅ webhook يصل (يظهر في ngrok)
- ✅ HTTP 200 response
- ✅ حالة الفاتورة = "Paid"
- ✅ سجل دفع جديد في WHMCS

## ⚠️ إذا واجهت مشاكل | If You Face Issues

### مشكلة: ngrok لا يعمل
```bash
# تحقق من التثبيت
ngrok version

# إعادة تشغيل
ngrok http 80
```

### مشكلة: webhook لا يصل
1. تحقق من ngrok interface: http://localhost:4040
2. تأكد من URL في لوحة تحكم لحظة
3. استخدم HTTPS URL (ليس HTTP)

### مشكلة: الفاتورة لا تتحدث
1. فحص Gateway Logs في WHMCS
2. تشغيل Test Suite للتشخيص
3. إضافة الدفع يدوياً كحل مؤقت

## 📞 ملفات المساعدة | Helper Files

- `WEBHOOK_TROUBLESHOOTING.md` - دليل حل المشاكل
- `NGROK_SETUP_GUIDE.md` - دليل ngrok مفصل
- `final_test_suite.php` - اختبارات شاملة

## 🎉 تهانينا مقدماً!

نظامك جاهز للعمل! فقط:
1. 🖱️ انقر نقرة مزدوجة على `START_LAHZA_WITH_TOKEN.bat`
2. 📋 انسخ webhook URL
3. 🔧 حدث لوحة تحكم لحظة
4. 🧪 اختبر الدفع

**كل شيء سيعمل بسلاسة!** 🚀

---

## 🔗 روابط سريعة | Quick Links

- **Lahza Dashboard**: https://dashboard.lahza.io
- **ngrok Dashboard**: https://dashboard.ngrok.com
- **Test Card**: **************** (CVV: 004, Exp: 03/30)

**ملاحظة**: أبق نافذة ngrok مفتوحة أثناء الاختبار!
