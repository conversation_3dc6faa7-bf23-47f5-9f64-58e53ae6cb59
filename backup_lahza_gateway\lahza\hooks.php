<?php
/**
 * Lahza.io Payment Gateway Hooks for WHMCS
 *
 * Action hooks for enhanced integration and functionality
 *
 * <AUTHOR> Development Team
 * @version 3.0.0
 * @link https://lahza.io/
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Hook: AdminAreaPage
 * Add Lahza.io management tools to admin area
 */
add_hook('AdminAreaPage', 1, function($vars) {
    if ($vars['filename'] == 'configgateways') {
        return array(
            'lahzaAdminTools' => '
            <script>
            document.addEventListener("DOMContentLoaded", function() {
                // Add Lahza.io specific admin tools
                const lahzaSection = document.querySelector(\'[data-gateway="lahza"]\');
                if (lahzaSection) {
                    const toolsHtml = `
                        <div class="lahza-admin-tools" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                            <h5><i class="fas fa-tools"></i> Lahza.io Tools</h5>
                            <div class="btn-group" role="group">
                                <a href="javascript:void(0)" onclick="testLahzaConnection()" class="btn btn-sm btn-info">
                                    <i class="fas fa-plug"></i> Test Connection
                                </a>
                                <a href="javascript:void(0)" onclick="viewLahzaLogs()" class="btn btn-sm btn-secondary">
                                    <i class="fas fa-list"></i> View Logs
                                </a>
                                <a href="https://docs.lahza.io/" target="_blank" class="btn btn-sm btn-primary">
                                    <i class="fas fa-book"></i> Documentation
                                </a>
                            </div>
                        </div>
                    `;
                    lahzaSection.insertAdjacentHTML("beforeend", toolsHtml);
                }
            });
            
            function testLahzaConnection() {
                // Test API connection
                fetch("/admin/configgateways.php?action=test_lahza_connection")
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert("✅ Connection successful!");
                        } else {
                            alert("❌ Connection failed: " + data.message);
                        }
                    })
                    .catch(error => {
                        alert("❌ Error testing connection: " + error.message);
                    });
            }
            
            function viewLahzaLogs() {
                window.open("/admin/systemmodulelog.php?module=lahza", "_blank");
            }
            </script>
            '
        );
    }
});

/**
 * Hook: InvoiceCreated
 * Log invoice creation for Lahza payments
 */
add_hook('InvoiceCreated', 1, function($vars) {
    $invoiceId = $vars['invoiceid'];
    
    // Check if this invoice uses Lahza gateway
    $invoice = localAPI('GetInvoice', array('invoiceid' => $invoiceId));
    
    if ($invoice['result'] == 'success' && $invoice['paymentmethod'] == 'lahza') {
        logActivity("Lahza Invoice Created - Invoice ID: {$invoiceId}");
    }
});

/**
 * Hook: InvoicePaid
 * Enhanced logging for Lahza payments
 */
add_hook('InvoicePaid', 1, function($vars) {
    $invoiceId = $vars['invoiceid'];
    $paymentMethod = $vars['paymentmethod'];
    
    if ($paymentMethod == 'lahza') {
        // Get transaction details
        $transactions = localAPI('GetInvoice', array(
            'invoiceid' => $invoiceId
        ));
        
        if ($transactions['result'] == 'success') {
            $lastTransaction = end($transactions['transactions']);
            
            logActivity("Lahza Payment Completed - Invoice: {$invoiceId}, Transaction: {$lastTransaction['transid']}, Amount: {$lastTransaction['amountin']}");
            
            // Optional: Send notification email to admin
            $gatewayParams = getGatewayVariables('lahza');
            if ($gatewayParams['enableLogging']) {
                // Log detailed payment information
                logTransaction('lahza', array(
                    'Invoice ID' => $invoiceId,
                    'Transaction ID' => $lastTransaction['transid'],
                    'Amount' => $lastTransaction['amountin'],
                    'Currency' => $lastTransaction['currency'],
                    'Date' => $lastTransaction['date'],
                    'Status' => 'Completed'
                ), 'Payment Completed via Hook');
            }
        }
    }
});

/**
 * Hook: InvoicePaymentReminder
 * Customize payment reminders for Lahza invoices
 */
add_hook('InvoicePaymentReminder', 1, function($vars) {
    $invoiceId = $vars['invoiceid'];
    
    // Check if invoice uses Lahza
    $invoice = localAPI('GetInvoice', array('invoiceid' => $invoiceId));
    
    if ($invoice['result'] == 'success' && $invoice['paymentmethod'] == 'lahza') {
        // Add Lahza-specific payment instructions to reminder
        return array(
            'lahzaPaymentInstructions' => '
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                    <h4 style="color: #667eea; margin-bottom: 10px;">
                        <i class="fas fa-bolt"></i> Pay with Lahza.io
                    </h4>
                    <p>You can pay this invoice securely using:</p>
                    <ul>
                        <li><i class="fas fa-credit-card"></i> Credit/Debit Cards</li>
                        <li><i class="fas fa-university"></i> Bank Transfer</li>
                        <li><i class="fas fa-mobile-alt"></i> Mobile Money</li>
                        <li><i class="fas fa-qrcode"></i> QR Code Payment</li>
                    </ul>
                    <p><small><i class="fas fa-shield-alt"></i> All payments are secured with 256-bit SSL encryption</small></p>
                </div>
            '
        );
    }
});

/**
 * Hook: ClientAreaPage
 * Enhance client area for Lahza payments
 */
add_hook('ClientAreaPage', 1, function($vars) {
    if ($vars['templatefile'] == 'viewinvoice') {
        $invoiceId = $vars['invoiceid'];
        
        // Check if this invoice uses Lahza
        $invoice = localAPI('GetInvoice', array('invoiceid' => $invoiceId));
        
        if ($invoice['result'] == 'success' && $invoice['paymentmethod'] == 'lahza') {
            return array(
                'lahzaPaymentInfo' => array(
                    'gateway' => 'lahza',
                    'supportedMethods' => array('card', 'bank', 'mobile_money', 'qr'),
                    'securityFeatures' => array('ssl', 'pci_compliant', 'fraud_protection')
                )
            );
        }
    }
});

/**
 * Hook: AdminInvoicesControlsOutput
 * Add Lahza-specific admin controls for invoices
 */
add_hook('AdminInvoicesControlsOutput', 1, function($vars) {
    $invoiceId = $vars['invoiceid'];
    
    // Check if invoice uses Lahza
    $invoice = localAPI('GetInvoice', array('invoiceid' => $invoiceId));
    
    if ($invoice['result'] == 'success' && $invoice['paymentmethod'] == 'lahza') {
        return '
        <div class="lahza-admin-controls">
            <button type="button" class="btn btn-info btn-sm" onclick="viewLahzaTransactionDetails(' . $invoiceId . ')">
                <i class="fas fa-eye"></i> View Lahza Details
            </button>
            <button type="button" class="btn btn-warning btn-sm" onclick="resendLahzaWebhook(' . $invoiceId . ')">
                <i class="fas fa-redo"></i> Resend Webhook
            </button>
        </div>
        
        <script>
        function viewLahzaTransactionDetails(invoiceId) {
            // Open modal with Lahza transaction details
            window.open("/admin/invoices.php?action=lahza_details&id=" + invoiceId, "_blank", "width=800,height=600");
        }
        
        function resendLahzaWebhook(invoiceId) {
            if (confirm("Resend webhook for this invoice?")) {
                fetch("/admin/invoices.php?action=resend_lahza_webhook&id=" + invoiceId)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert("✅ Webhook resent successfully!");
                        } else {
                            alert("❌ Failed to resend webhook: " + data.message);
                        }
                    });
            }
        }
        </script>
        ';
    }
});

/**
 * Hook: DailyCronJob
 * Daily maintenance tasks for Lahza gateway
 */
add_hook('DailyCronJob', 1, function($vars) {
    // Clean up old logs (keep last 30 days)
    $cutoffDate = date('Y-m-d', strtotime('-30 days'));
    
    // Clean up transaction logs
    $result = full_query("DELETE FROM tblgatewaylog WHERE gateway = 'lahza' AND date < '{$cutoffDate}'");
    
    if ($result) {
        logActivity("Lahza Daily Cleanup - Removed old gateway logs");
    }
    
    // Check for failed webhooks and retry
    $gatewayParams = getGatewayVariables('lahza');
    if ($gatewayParams['enableLogging']) {
        // Log daily statistics
        $stats = full_query("
            SELECT 
                COUNT(*) as total_transactions,
                SUM(CASE WHEN result = 'Successful' THEN 1 ELSE 0 END) as successful,
                SUM(CASE WHEN result != 'Successful' THEN 1 ELSE 0 END) as failed
            FROM tblgatewaylog 
            WHERE gateway = 'lahza' 
            AND date >= CURDATE()
        ");
        
        if ($stats) {
            $row = mysql_fetch_assoc($stats);
            logActivity("Lahza Daily Stats - Total: {$row['total_transactions']}, Success: {$row['successful']}, Failed: {$row['failed']}");
        }
    }
});

/**
 * Hook: AfterModuleCreate
 * Post-installation setup for Lahza gateway
 */
add_hook('AfterModuleCreate', 1, function($vars) {
    if ($vars['moduleType'] == 'gateway' && $vars['moduleName'] == 'lahza') {
        // Set default configuration
        $defaultConfig = array(
            'testMode' => 'on',
            'enableLogging' => 'on',
            'paymentMethod' => 'popup',
            'allowedChannels' => 'card,bank,mobile_money',
            'ipWhitelist' => '*************,**************'
        );
        
        foreach ($defaultConfig as $setting => $value) {
            full_query("
                INSERT INTO tblpaymentgateways (gateway, setting, value) 
                VALUES ('lahza', '{$setting}', '{$value}')
                ON DUPLICATE KEY UPDATE value = '{$value}'
            ");
        }
        
        logActivity("Lahza Gateway - Default configuration applied");
    }
});

/**
 * Hook: ShoppingCartCheckoutCompletePage
 * Track checkout completion for analytics
 */
add_hook('ShoppingCartCheckoutCompletePage', 1, function($vars) {
    if ($vars['paymentmethod'] == 'lahza') {
        // Track successful checkout
        logActivity("Lahza Checkout Completed - Order ID: {$vars['orderid']}, Invoice ID: {$vars['invoiceid']}");
        
        // Optional: Send conversion tracking
        return array(
            'lahzaConversionTracking' => array(
                'orderId' => $vars['orderid'],
                'invoiceId' => $vars['invoiceid'],
                'amount' => $vars['amount'],
                'currency' => $vars['currency']
            )
        );
    }
});

/**
 * Custom function to get Lahza transaction statistics
 */
function getLahzaTransactionStats($days = 30) {
    $cutoffDate = date('Y-m-d', strtotime("-{$days} days"));
    
    $query = "
        SELECT 
            DATE(date) as transaction_date,
            COUNT(*) as total_transactions,
            SUM(CASE WHEN result = 'Successful' THEN 1 ELSE 0 END) as successful_transactions,
            SUM(CASE WHEN result != 'Successful' THEN 1 ELSE 0 END) as failed_transactions
        FROM tblgatewaylog 
        WHERE gateway = 'lahza' 
        AND date >= '{$cutoffDate}'
        GROUP BY DATE(date)
        ORDER BY transaction_date DESC
    ";
    
    $result = full_query($query);
    $stats = array();
    
    while ($row = mysql_fetch_assoc($result)) {
        $stats[] = $row;
    }
    
    return $stats;
}

/**
 * Custom function to validate Lahza webhook signature
 */
function validateLahzaWebhookSignature($payload, $signature, $secret) {
    $expectedSignature = hash_hmac('sha256', $payload, $secret);
    $providedSignature = str_replace('sha256=', '', $signature);
    
    return hash_equals($expectedSignature, $providedSignature);
}