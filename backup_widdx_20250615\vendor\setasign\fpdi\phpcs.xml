<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PHP_CodeSniffer"
         xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd"
>
    <file>src</file>
<!--    <file>tests</file>-->

    <arg name="basepath" value="."/>
    <arg name="colors"/>
    <arg name="parallel" value="75"/>

    <rule ref="PSR12">
        <exclude name="PSR1.Methods.CamelCapsMethodName.NotCamelCaps"/>
        <exclude name="PSR2.Methods.MethodDeclaration.Underscore"/>
        <exclude name="PSR12.Properties.ConstantVisibility.NotFound"/>
    </rule>
</ruleset>