# دليل اختبار بوابة الدفع لحظة - Lahza Payment Gateway Testing Guide

## 🎯 الهدف | Objective
التأكد من أن بوابة الدفع لحظة تعمل بشكل صحيح وتقوم بتحديث حالة الفواتير في WHMCS عند نجاح الدفع.

## ✅ المتطلبات المسبقة | Prerequisites

### 1. إعدادات WHMCS
- تفعيل بوابة لحظة: `Admin → Setup → Payments → Payment Gateways → Lahza.io`
- تفعيل وضع الاختبار: `Test Mode: Yes`
- إدخال مفاتيح API الاختبار:
  - Public Key: `pk_test_0e9Ce7p2rYbMd5Gjwi1JSSfnUUmBs98eQ`
  - Secret Key: `sk_test_kAFTeibRx3HS66tP0MfeBYqAviilBb7im`
- تفعيل السجلات: `Enable Logging: Yes`

### 2. إعدادات لحظة | Lahza Settings
- تسجيل الدخول إلى لوحة تحكم لحظة
- إضافة Webhook URL: `http://localhost/Whmcs/modules/gateways/callback/lahza.php`
- تفعيل webhook events: `charge.success`, `charge.failed`

## 🧪 خطوات الاختبار | Testing Steps

### الاختبار 1: إنشاء فاتورة جديدة
1. انتقل إلى: `Admin → Billing → Create Invoice`
2. اختر عميل موجود أو أنشئ عميل جديد
3. أضف منتج أو خدمة
4. **مهم**: تأكد من تحديد `Payment Method: Lahza.io`
5. احفظ الفاتورة

### الاختبار 2: معالجة الدفع
1. انتقل إلى الفاتورة من جهة العميل
2. اضغط على "Pay Now"
3. استخدم بيانات بطاقة الاختبار:
   - **رقم البطاقة**: `4111111111111111`
   - **CVV**: `004`
   - **تاريخ الانتهاء**: `03/30`
4. أكمل عملية الدفع

### الاختبار 3: التحقق من النتائج
بعد نجاح الدفع، يجب أن يحدث التالي:

#### ✅ إعادة التوجيه الصحيحة
- يتم توجيهك إلى: `http://localhost/Whmcs/viewinvoice.php?id=XX`
- **لا** يجب أن تظهر رسالة "Payment processing error"

#### ✅ تحديث حالة الفاتورة
- حالة الفاتورة تتغير إلى: `Paid`
- يظهر سجل الدفع في تفاصيل الفاتورة
- يتم إضافة Transaction ID

#### ✅ السجلات
- فحص Gateway Logs: `Admin → Logs → Gateway Log`
- فحص Module Logs: `Admin → Logs → Module Log`
- يجب أن تظهر رسائل نجاح العملية

## 🔍 استكشاف الأخطاء | Troubleshooting

### مشكلة: "Payment processing error"
**الحل:**
1. تحقق من إعدادات SystemURL في قاعدة البيانات
2. تأكد من تفعيل بوابة لحظة
3. فحص سجلات الأخطاء

### مشكلة: الفاتورة لا تتحدث إلى "Paid"
**الحل:**
1. تحقق من webhook URL في لوحة تحكم لحظة
2. تأكد من صحة API keys
3. فحص Gateway Logs للأخطاء

### مشكلة: Webhook لا يصل
**الحل:**
1. تأكد من إمكانية الوصول إلى callback URL
2. فحص firewall settings
3. تحقق من webhook signature

## 📋 قائمة التحقق | Checklist

- [ ] بوابة لحظة مفعلة في WHMCS
- [ ] API keys صحيحة ومحدثة
- [ ] وضع الاختبار مفعل
- [ ] Webhook URL محدد في لحظة
- [ ] SystemURL محدد بشكل صحيح
- [ ] السجلات مفعلة
- [ ] فاتورة اختبار منشأة
- [ ] Payment method محدد كـ Lahza
- [ ] الدفع تم بنجاح
- [ ] إعادة التوجيه تعمل
- [ ] حالة الفاتورة محدثة
- [ ] سجل الدفع مضاف

## 🔗 روابط مفيدة | Useful Links

### WHMCS Admin
- [Gateway Configuration](http://localhost/Whmcs/admin/configgateways.php)
- [Invoice Management](http://localhost/Whmcs/admin/systeminvoices.php)
- [Gateway Logs](http://localhost/Whmcs/admin/systemgatewaylog.php)
- [Module Logs](http://localhost/Whmcs/admin/systemmodulelog.php)

### Test URLs
- [Test Invoice 12](http://localhost/Whmcs/viewinvoice.php?id=12)
- [Callback Test](http://localhost/Whmcs/modules/gateways/callback/lahza.php?reference=INV-12-test)

## 📞 الدعم | Support

إذا واجهت أي مشاكل:
1. فحص السجلات أولاً
2. التأكد من الإعدادات
3. اختبار callback URL يدوياً
4. مراجعة وثائق لحظة API

---

**ملاحظة مهمة**: تأكد من استخدام بيانات الاختبار فقط في وضع التطوير.
