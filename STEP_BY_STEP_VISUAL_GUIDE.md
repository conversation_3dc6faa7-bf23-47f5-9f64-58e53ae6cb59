# 📸 دليل مصور خطوة بخطوة - Visual Step-by-Step Guide

## 🎯 سأوضح لك كل خطوة بالتفصيل

---

## 🚀 الخطوة 1: تشغيل الملف

### ما تفعله:
1. اذهب إلى مجلد: `C:\xampp\htdocs\Whmcs\`
2. ابحث عن ملف: `START_LAHZA_WITH_TOKEN.bat`
3. **انقر نقرة مزدوجة** على الملف

### ما ستراه:
```
نافذة سوداء (Command Prompt) ستظهر مع نص ملون:

 ██╗      █████╗ ██╗  ██╗███████╗ █████╗ 
 ██║     ██╔══██╗██║  ██║╚══███╔╝██╔══██╗
 ██║     ███████║███████║  ███╔╝ ███████║
 ██║     ██╔══██║██╔══██║ ███╔╝  ██╔══██║
 ███████╗██║  ██║██║  ██║███████╗██║  ██║
 ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝

Payment Gateway - Auto Setup with Your Token
============================================

[INFO] Starting Lahza Payment Gateway with your pre-configured token...
```

---

## ⚙️ الخطوة 2: إذا ظهرت رسالة XAMPP

### إذا رأيت:
```
[WARNING] Apache not running on port 80
[INFO] Starting XAMPP Control Panel...
Please start Apache and MySQL services
Then press any key to continue...
```

### ما تفعله:
1. ستُفتح نافذة **XAMPP Control Panel** تلقائياً
2. اضغط على زر **"Start"** بجانب **Apache**
3. اضغط على زر **"Start"** بجانب **MySQL**
4. انتظر حتى يصبح لونهما أخضر
5. ارجع للنافذة السوداء واضغط **أي مفتاح**

### إذا رأيت:
```
[SUCCESS] Apache is running on port 80
```
**ممتاز! تابع للخطوة التالية**

---

## 🔧 الخطوة 3: إعداد ngrok

### ما ستراه:
```
[STEP 2] Setting up ngrok with your token...
[SUCCESS] ngrok found
[INFO] Configuring ngrok with your auth token...
[SUCCESS] Auth token configured successfully
```

### إذا رأيت رسالة خطأ:
```
[ERROR] ngrok is not installed
```

### ما تفعله:
1. ستُفتح صفحة تحميل ngrok تلقائياً
2. حمل ngrok لنظام Windows
3. استخرج الملف إلى مجلد `C:\ngrok\`
4. أضف `C:\ngrok\` إلى PATH في Windows
5. أعد تشغيل الملف

---

## 🌐 الخطوة 4: بدء Tunnel

### ما ستراه:
```
[STEP 3] Starting ngrok tunnel...
[INFO] Starting ngrok tunnel for port 80...
[INFO] Waiting for ngrok to initialize...
```

### ثم ستُفتح نافذة جديدة بعنوان "ngrok-tunnel"

---

## 📱 الخطوة 5: فتح الواجهات

### ما سيحدث تلقائياً:
ستُفتح 5 تبويبات في المتصفح:

1. **ngrok Interface**: `http://localhost:4040`
2. **Webhook Updater**: `http://localhost/Whmcs/update_lahza_webhook.php`
3. **WHMCS Admin**: `http://localhost/Whmcs/admin/`
4. **Test Suite**: `http://localhost/Whmcs/final_test_suite.php`
5. **Lahza Dashboard**: `https://dashboard.lahza.io`

---

## 📋 الخطوة 6: نسخ Webhook URL

### في تبويب "Webhook Updater":
ستجد مربع أخضر يحتوي على:
```
✅ ngrok Tunnel Detected
Public URL: https://abc123.ngrok.io
Webhook URL:
https://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php

[📋 Copy Webhook URL]  ← اضغط هذا الزر
```

### ما تفعله:
1. اضغط زر **"Copy Webhook URL"**
2. ستظهر رسالة: "Webhook URL copied to clipboard!"

---

## 🏢 الخطوة 7: تحديث لوحة تحكم لحظة

### في تبويب "Lahza Dashboard":
1. **سجل الدخول** بحسابك في لحظة
2. ابحث عن قسم **"Webhooks"** أو **"API Settings"**
3. ابحث عن حقل **"Webhook URL"**
4. **امسح** URL القديم
5. **الصق** URL الجديد (Ctrl+V)
6. تأكد من تفعيل:
   - ✅ `charge.success`
   - ✅ `charge.failed`
7. اضغط **"Save"** أو **"حفظ"**

---

## 🧪 الخطوة 8: اختبار الدفع

### في تبويب "WHMCS Admin":
1. اذهب إلى: **Billing → Create Invoice**
2. اختر عميل موجود أو أنشئ جديد
3. أضف منتج بمبلغ $10
4. **مهم جداً**: في **Payment Method** اختر **"Lahza"**
5. احفظ الفاتورة

### معالجة الدفع:
1. اذهب للفاتورة من جهة العميل
2. اضغط **"Pay Now"**
3. أدخل بيانات بطاقة الاختبار:
   - **رقم البطاقة**: `4111111111111111`
   - **CVV**: `004`
   - **تاريخ الانتهاء**: `03/30`
4. اضغط **"Pay"**

---

## ✅ الخطوة 9: التحقق من النجاح

### في تبويب "ngrok Interface":
ستظهر طلبات HTTP مثل:
```
POST /Whmcs/modules/gateways/callback/lahza.php  200 OK
```

### في WHMCS:
- حالة الفاتورة ستصبح **"Paid"** بدلاً من **"Unpaid"**
- سيظهر سجل دفع جديد

---

## 🆘 إذا واجهت مشكلة

### المشاكل الشائعة:

#### 1. ngrok لا يعمل:
```bash
# في Command Prompt
cd C:\ngrok
ngrok version
```

#### 2. webhook لا يصل:
- تحقق من ngrok Interface
- تأكد من URL في لوحة تحكم لحظة
- استخدم HTTPS (ليس HTTP)

#### 3. الفاتورة لا تتحدث:
- فحص Gateway Logs في WHMCS
- تشغيل Test Suite
- إضافة الدفع يدوياً

---

## 📞 تحتاج مساعدة؟

### أخبرني في أي خطوة أنت:
- "وصلت للخطوة 3 لكن ngrok لا يعمل"
- "فتحت الواجهات لكن لا أجد webhook URL"
- "حدثت لوحة تحكم لحظة لكن webhook لا يصل"

### سأساعدك فوراً! 🚀

---

## 🎯 ملخص سريع:

1. **انقر نقرة مزدوجة**: `START_LAHZA_WITH_TOKEN.bat`
2. **ابدأ XAMPP** إذا طُلب منك
3. **انسخ webhook URL** من تبويب Webhook Updater
4. **حدث لوحة تحكم لحظة** بـ URL الجديد
5. **اختبر الدفع** ببطاقة `4111111111111111`
6. **تحقق من النجاح** في ngrok Interface

**ابدأ الآن وأخبرني عند أي خطوة!** 🎉
