# دليل إعداد ngrok لحل مشكلة Webhook
# ngrok Setup Guide for Webhook Resolution

## 🎯 الهدف | Objective
حل مشكلة عدم وصول webhook من لحظة إلى localhost عبر إنشاء tunnel عام باستخدام ngrok.

## 📥 تحميل وتثبيت ngrok | Download and Install ngrok

### الطريقة 1: التحميل المباشر | Direct Download
1. اذهب إلى: https://ngrok.com/download
2. حمل النسخة المناسبة لنظام التشغيل (Windows/Mac/Linux)
3. استخرج الملف إلى مجلد مناسب

### الطريقة 2: عبر Package Manager
```bash
# Windows (Chocolatey)
choco install ngrok

# macOS (Homebrew)
brew install ngrok/ngrok/ngrok

# Linux (Snap)
sudo snap install ngrok
```

## 🔧 إعداد ngrok | ngrok Configuration

### 1. إنشاء حساب مجاني | Create Free Account
1. اذه<PERSON> إلى: https://dashboard.ngrok.com/signup
2. أنشئ حساب مجاني
3. احصل على Auth Token من Dashboard

### 2. ربط Auth Token | Connect Auth Token
```bash
ngrok config add-authtoken YOUR_AUTH_TOKEN_HERE
```

### 3. تشغيل ngrok للـ WHMCS | Run ngrok for WHMCS
```bash
# إذا كان XAMPP يعمل على port 80
ngrok http 80

# إذا كان يعمل على port مختلف (مثل 8080)
ngrok http 8080

# مع subdomain مخصص (يتطلب حساب مدفوع)
ngrok http 80 --subdomain=your-custom-name
```

## 📋 خطوات التنفيذ | Implementation Steps

### الخطوة 1: تشغيل XAMPP
```bash
# تأكد من تشغيل Apache و MySQL
# افتح XAMPP Control Panel
# ابدأ Apache و MySQL
```

### الخطوة 2: تشغيل ngrok
```bash
# في Command Prompt أو Terminal
ngrok http 80
```

### الخطوة 3: نسخ URL العام | Copy Public URL
بعد تشغيل ngrok، ستحصل على output مثل:
```
ngrok by @inconshreveable

Session Status                online
Account                       <EMAIL>
Version                       3.0.0
Region                        United States (us)
Latency                       45ms
Web Interface                 http://127.0.0.1:4040
Forwarding                    https://abc123.ngrok.io -> http://localhost:80
Forwarding                    http://abc123.ngrok.io -> http://localhost:80

Connections                   ttl     opn     rt1     rt5     p50     p90
                              0       0       0.00    0.00    0.00    0.00
```

**انسخ URL**: `https://abc123.ngrok.io`

### الخطوة 4: تحديث webhook URL في لحظة | Update Webhook URL in Lahza

1. سجل الدخول إلى لوحة تحكم لحظة
2. اذهب إلى إعدادات Webhook
3. أدخل URL الجديد:
   ```
   https://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php
   ```
4. احفظ الإعدادات

### الخطوة 5: اختبار النظام | Test the System

1. أنشئ فاتورة جديدة في WHMCS
2. اختر بوابة لحظة للدفع
3. أكمل عملية الدفع
4. تحقق من وصول webhook في ngrok interface: `http://127.0.0.1:4040`

## 🔍 مراقبة Webhook | Webhook Monitoring

### ngrok Web Interface
- افتح: `http://127.0.0.1:4040`
- راقب الطلبات الواردة
- فحص webhook requests من لحظة

### WHMCS Gateway Logs
- `Admin → Logs → Gateway Log`
- ابحث عن سجلات "lahza"
- تحقق من وصول webhook

## ⚠️ نصائح مهمة | Important Tips

### 1. استقرار الاتصال | Connection Stability
- أبق ngrok يعمل طوال فترة الاختبار
- لا تغلق Terminal/Command Prompt
- URL يتغير عند إعادة تشغيل ngrok

### 2. الأمان | Security
- استخدم HTTPS URL دائماً
- لا تشارك ngrok URL مع الآخرين
- أغلق ngrok عند انتهاء الاختبار

### 3. الحدود المجانية | Free Tier Limits
- 1 tunnel متزامن
- URL عشوائي (يتغير عند إعادة التشغيل)
- 40 connections/minute

## 🚀 اختبار شامل | Comprehensive Testing

### سيناريو الاختبار الكامل | Complete Test Scenario

1. **تشغيل البيئة**:
   ```bash
   # Terminal 1: تشغيل XAMPP
   # Terminal 2: تشغيل ngrok
   ngrok http 80
   ```

2. **تحديث إعدادات لحظة**:
   - Webhook URL: `https://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php`

3. **إنشاء فاتورة اختبار**:
   - مبلغ: $10.00
   - Payment Method: Lahza

4. **معالجة الدفع**:
   - بطاقة اختبار: `4111111111111111`
   - CVV: `004`
   - Expiry: `03/30`

5. **التحقق من النتائج**:
   - ✅ webhook وصل (ngrok interface)
   - ✅ حالة الفاتورة: "Paid"
   - ✅ سجل الدفع مضاف
   - ✅ إعادة توجيه صحيحة

## 🔧 استكشاف الأخطاء | Troubleshooting

### مشكلة: ngrok لا يعمل
```bash
# تحقق من التثبيت
ngrok version

# تحقق من Auth Token
ngrok config check

# إعادة تعيين Auth Token
ngrok config add-authtoken YOUR_NEW_TOKEN
```

### مشكلة: webhook لا يصل
1. تحقق من ngrok interface: `http://127.0.0.1:4040`
2. تأكد من URL في لوحة تحكم لحظة
3. فحص WHMCS Gateway Logs

### مشكلة: SSL Certificate
```bash
# استخدم HTTPS URL دائماً
https://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php

# وليس HTTP
http://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php
```

## 📱 للإنتاج | For Production

### بدائل ngrok للإنتاج | Production Alternatives

1. **خادم حقيقي | Real Server**
   - استضافة WHMCS على خادم حقيقي
   - استخدام domain حقيقي مع SSL

2. **خدمات أخرى | Other Services**
   - Cloudflare Tunnel
   - LocalTunnel
   - Serveo

3. **VPS/Cloud Hosting**
   - DigitalOcean
   - AWS EC2
   - Google Cloud

## ✅ قائمة التحقق | Checklist

- [ ] ngrok مثبت ومُعد
- [ ] Auth Token مُضاف
- [ ] XAMPP يعمل على port 80
- [ ] ngrok tunnel نشط
- [ ] URL العام منسوخ
- [ ] webhook URL محدث في لحظة
- [ ] HTTPS مستخدم (وليس HTTP)
- [ ] اختبار الدفع تم بنجاح
- [ ] webhook وصل ومُعالج
- [ ] حالة الفاتورة محدثة

---

**ملاحظة**: ngrok حل مؤقت للتطوير. للإنتاج، استخدم خادم حقيقي مع domain مناسب.
