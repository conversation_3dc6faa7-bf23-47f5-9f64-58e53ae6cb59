<?php
/**
 * Developed by WIDDX (https://widdx.com)
 * © 2025 All Rights Reserved
 * 
 * Lahza Payment Gateway Callback Handler for WHMCS
 */

// Require libraries needed for gateway module functions.
require_once __DIR__ . '/../../../init.php';
require_once __DIR__ . '/../../../includes/gatewayfunctions.php';
require_once __DIR__ . '/../../../includes/invoicefunctions.php';

require_once __DIR__ . '/../lahza/lib/LahzaApiHandler.php';

// Detect module name from filename.
$gatewayModuleName = basename(__FILE__, '.php');

// Fetch gateway configuration parameters.
$gatewayParams = getGatewayVariables($gatewayModuleName);

// Die if module is not active.
if (!$gatewayParams['type']) {
    die("Module Not Activated");
}

// Initialize response
$response = array('status' => 'error', 'message' => 'Invalid request');

try {
    // Get request method
    $requestMethod = $_SERVER['REQUEST_METHOD'];
    
    if ($requestMethod === 'POST') {
        // Handle webhook notification
        handleWebhook($gatewayParams);
    } elseif ($requestMethod === 'GET') {
        // Handle return from payment page
        handleReturn($gatewayParams);
    } else {
        throw new Exception('Invalid request method');
    }

} catch (Exception $e) {
    // Log error with more details
    logTransaction($gatewayParams['name'], array(
        'error' => $e->getMessage(),
        'request_method' => $requestMethod,
        'get_params' => $_GET,
        'post_params' => $_POST,
        'server_info' => array(
            'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? '',
            'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? '',
            'HTTPS' => $_SERVER['HTTPS'] ?? ''
        )
    ), 'Callback Error: ' . $e->getMessage());

    // Return error response for webhooks
    if ($requestMethod === 'POST') {
        http_response_code(400);
        echo json_encode(array('status' => 'error', 'message' => $e->getMessage()));
    } else {
        // Enhanced error handling for GET requests
        $invoiceId = null;
        $reference = '';

        // Try to get reference from multiple sources
        if (isset($_GET['reference'])) {
            $reference = $_GET['reference'];
        } elseif (isset($_GET['trxref'])) {
            $reference = $_GET['trxref'];
        }

        if (!empty($reference)) {
            // Support multiple INV formats
            if (preg_match('/^INV-(\d+)-\d+-[a-f0-9]{8}$/', $reference, $matches)) {
                // New unique format: INV-123-1234567890-abcd1234
                $invoiceId = (int)$matches[1];
            } elseif (preg_match('/^INV-(\d+)$/', $reference, $matches)) {
                // Simple format: INV-123
                $invoiceId = (int)$matches[1];
            } elseif (is_numeric($reference)) {
                // Pure numeric format (legacy)
                $invoiceId = (int)$reference;
            }

            // Log the parsing attempt
            logTransaction($gatewayParams['name'], array(
                'reference' => $reference,
                'extracted_invoice_id' => $invoiceId,
                'parsing_successful' => !empty($invoiceId)
            ), 'Reference Parsing in Error Handler');
        }

        if ($invoiceId) {
            global $CONFIG;

            // Try multiple ways to get system URL
            $systemUrl = '';

            // Method 1: From WHMCS config
            if (!empty($CONFIG['SystemURL'])) {
                $systemUrl = rtrim($CONFIG['SystemURL'], '/');
            }

            // Method 2: Construct from server variables
            if (empty($systemUrl)) {
                $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
                $systemUrl = $protocol . $_SERVER['HTTP_HOST'];
            }

            // Method 3: Fallback to localhost
            if (empty($systemUrl)) {
                $systemUrl = 'http://localhost/Whmcs';
            }

            $redirectUrl = $systemUrl . '/viewinvoice.php?id=' . $invoiceId;

            // Log the redirect attempt
            logTransaction($gatewayParams['name'], array(
                'system_url' => $systemUrl,
                'redirect_url' => $redirectUrl,
                'invoice_id' => $invoiceId,
                'config_system_url' => $CONFIG['SystemURL'] ?? 'not_set'
            ), 'Redirecting to Invoice in Error Handler');

            header('Location: ' . $redirectUrl);
            exit;
        }

        // If we can't extract invoice ID, show detailed error
        $errorDetails = array(
            'error' => $e->getMessage(),
            'reference' => $reference,
            'get_params' => $_GET,
            'extracted_invoice_id' => $invoiceId
        );

        logTransaction($gatewayParams['name'], $errorDetails, 'Unable to Extract Invoice ID for Redirect');

        // Show user-friendly error with some debug info in test mode
        if ($gatewayParams['testMode']) {
            die('Payment processing error. Debug info: ' . json_encode($errorDetails));
        } else {
            die('Payment processing error. Please contact support.');
        }
    }
}

/**
 * Handle webhook notifications from Lahza
 *
 * @param array $gatewayParams Gateway configuration parameters
 */
function handleWebhook($gatewayParams)
{
    global $gatewayModuleName;
    
    // Get raw POST data
    $rawPayload = file_get_contents('php://input');
    
    // Log raw payload for debugging
    logTransaction($gatewayParams['name'], array('raw_payload' => $rawPayload), 'Raw Webhook Payload Received');
    
    if (empty($rawPayload)) {
        throw new Exception('Empty webhook payload');
    }
    
    $event = json_decode($rawPayload, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON payload: ' . json_last_error_msg());
    }
    
    // Check webhook event type if available
    $eventType = isset($event['event']) ? $event['event'] : 'unknown';

    // Log parsed event for debugging
    logTransaction($gatewayParams['name'], array(
        'event_type' => $eventType,
        'event_data' => $event,
        'payload_size' => strlen($rawPayload)
    ), 'Parsed Webhook Event - Type: ' . $eventType);

    // Handle different webhook event types
    switch ($eventType) {
        case 'charge.success':
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Successful Charge Event');
            break;
        case 'charge.failed':
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Failed Charge Event');
            break;
        case 'refund.processed':
        case 'refund.failed':
        case 'refund.pending':
        case 'refund.processing':
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Refund Event');
            // Handle refund events separately if needed
            break;
        default:
            logTransaction($gatewayParams['name'], array('event_type' => $eventType), 'Processing Generic/Unknown Event');
            break;
    }
    
    // Get signature from headers - try multiple possible header names
    $signature = '';
    $possibleHeaders = array(
        'HTTP_X_LAHZA_SIGNATURE',
        'HTTP_X_SIGNATURE',
        'HTTP_LAHZA_SIGNATURE',
        'HTTP_SIGNATURE'
    );

    foreach ($possibleHeaders as $header) {
        if (isset($_SERVER[$header]) && !empty($_SERVER[$header])) {
            $signature = $_SERVER[$header];
            break;
        }
    }

    // Log all headers for debugging
    logTransaction($gatewayParams['name'], array(
        'all_headers' => getallheaders(),
        'server_vars' => array_filter($_SERVER, function($key) {
            return strpos($key, 'HTTP_') === 0;
        }, ARRAY_FILTER_USE_KEY),
        'found_signature' => $signature
    ), 'Webhook Headers Analysis');

    // FIXED: Enhanced security validation
    if (empty($signature)) {
        if ($gatewayParams['testMode']) {
            // In test mode, log warning but add additional security checks
            logTransaction($gatewayParams['name'], array(
                'warning' => 'Missing webhook signature in test mode',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'unknown'
            ), 'Test Mode Signature Warning');
            
            // Additional security check in test mode
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (strpos($contentType, 'application/json') === false) {
                throw new Exception('Invalid content type for webhook in test mode');
            }
        } else {
            // In production mode, signature is mandatory
            logTransaction($gatewayParams['name'], array(
                'error' => 'Missing webhook signature in production mode',
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'headers' => getallheaders()
            ), 'Production Mode Security Error');
            throw new Exception('Missing webhook signature in production mode');
        }
    } else {
        // Verify signature
        $calculatedSignature = hash_hmac('sha256', $rawPayload, $gatewayParams['secretKey']);

        if (!hash_equals($signature, $calculatedSignature)) {
            logTransaction($gatewayParams['name'], array(
                'received_signature' => $signature,
                'calculated_signature' => $calculatedSignature,
                'payload_length' => strlen($rawPayload),
                'secret_key_length' => strlen($gatewayParams['secretKey']),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ), 'Signature Verification Failed');
            throw new Exception('Invalid webhook signature');
        }
        
        logTransaction($gatewayParams['name'], array(
            'signature_verified' => true,
            'signature_length' => strlen($signature)
        ), 'Signature Verification Successful');
    }
    
    // Extract data from webhook with error checking - try multiple possible structures
    $data = null;
    $invoiceId = null;
    $clientId = null;
    $transactionId = null;
    $status = null;
    $amount = 0;
    $fee = 0;

    // Try different possible webhook structures
    if (isset($event['data']) && is_array($event['data'])) {
        $data = $event['data'];
    } elseif (isset($event['transaction']) && is_array($event['transaction'])) {
        $data = $event['transaction'];
    } elseif (is_array($event)) {
        $data = $event; // Sometimes the data is at the root level
    }

    if (!$data) {
        throw new Exception('Invalid webhook data structure - no data found');
    }

    // Extract required fields with multiple fallback options
    // Invoice ID - Enhanced extraction with clear WHMCS ID support
    if (isset($data['metadata']['invoiceid'])) {
        $invoiceId = $data['metadata']['invoiceid'];
    } elseif (isset($data['metadata']['invoice_id'])) {
        $invoiceId = $data['metadata']['invoice_id'];
    } elseif (isset($data['reference']) && preg_match('/INV-(\d+)-\d+/', $data['reference'], $matches)) {
        // Extract from clear reference format: INV-10-20250618012542-abc123
        $invoiceId = $matches[1];
    } elseif (isset($data['reference']) && preg_match('/INV-(\d+)/', $data['reference'], $matches)) {
        // Fallback to simple format: INV-10
        $invoiceId = $matches[1];
    } elseif (isset($data['metadata']['original_reference']) && preg_match('/INV-(\d+)/', $data['metadata']['original_reference'], $matches)) {
        $invoiceId = $matches[1];
    }

    // Client ID
    if (isset($data['metadata']['clientid'])) {
        $clientId = $data['metadata']['clientid'];
    } elseif (isset($data['metadata']['client_id'])) {
        $clientId = $data['metadata']['client_id'];
    }

    // Transaction ID
    if (isset($data['id'])) {
        $transactionId = $data['id'];
    } elseif (isset($data['transaction_id'])) {
        $transactionId = $data['transaction_id'];
    } elseif (isset($data['reference'])) {
        $transactionId = $data['reference'];
    }

    // Status
    if (isset($data['status'])) {
        $status = strtolower($data['status']);
    } elseif (isset($data['state'])) {
        $status = strtolower($data['state']);
    }

    // Amount (convert from cents to main currency unit)
    if (isset($data['amount'])) {
        $amount = is_numeric($data['amount']) ? ($data['amount'] / 100) : 0;
    }

    // Fee (convert from cents to main currency unit)
    if (isset($data['fees'])) {
        $fee = is_numeric($data['fees']) ? ($data['fees'] / 100) : 0;
    } elseif (isset($data['fee'])) {
        $fee = is_numeric($data['fee']) ? ($data['fee'] / 100) : 0;
    }
    
    // Validate required fields with enhanced error reporting
    if (empty($invoiceId) || empty($transactionId)) {
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'error' => 'Missing required fields',
            'reference' => isset($data['reference']) ? $data['reference'] : 'not_provided',
            'metadata' => isset($data['metadata']) ? $data['metadata'] : 'not_provided',
            'complete_data' => $data
        ), 'Webhook Validation Failed - Missing Required Fields');
        throw new Exception('Missing required webhook data fields: invoiceId or transactionId');
    }

    // Log all extracted data for debugging
    logTransaction($gatewayParams['name'], array(
        'invoice_id' => $invoiceId,
        'client_id' => $clientId,
        'transaction_id' => $transactionId,
        'status' => $status,
        'amount' => $amount,
        'fee' => $fee,
        'extraction_successful' => true
    ), 'Webhook Data Extracted Successfully');

    // Validate callback invoice ID
    try {
        checkCbInvoiceID($invoiceId, $gatewayParams['name']);
        logTransaction($gatewayParams['name'], array('invoice_id' => $invoiceId), 'Invoice ID Validation Passed');
    } catch (Exception $e) {
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'error' => $e->getMessage()
        ), 'Invoice ID Validation Failed');
        throw $e;
    }

    // Check for duplicate transactions
    try {
        checkCbTransID($transactionId);
        logTransaction($gatewayParams['name'], array('transaction_id' => $transactionId), 'Transaction ID Validation Passed');
    } catch (Exception $e) {
        logTransaction($gatewayParams['name'], array(
            'transaction_id' => $transactionId,
            'error' => $e->getMessage()
        ), 'Transaction ID Validation Failed - Possible Duplicate');
        throw $e;
    }

    // FIXED: Enhanced status handling with more comprehensive status arrays
    $successStatuses = array('success', 'successful', 'completed', 'paid', 'approved', 'confirmed', 'settled');
    $failedStatuses = array('failed', 'declined', 'cancelled', 'canceled', 'abandoned', 'rejected', 'expired', 'timeout');
    $pendingStatuses = array('pending', 'processing', 'initiated', 'ongoing', 'in_progress', 'waiting', 'review');

    if (in_array($status, $successStatuses)) {
        try {
            // Check if invoice exists and is unpaid before adding payment
            $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
            $stmt = $pdo->prepare("SELECT id, status FROM tblinvoices WHERE id = ?");
            $stmt->execute([$invoiceId]);
            $invoice = $stmt->fetch(\PDO::FETCH_OBJ);

            if (!$invoice) {
                throw new Exception("Invoice {$invoiceId} not found");
            }

            if ($invoice->status === 'Paid') {
                logTransaction($gatewayParams['name'], array(
                    'invoice_id' => $invoiceId,
                    'current_status' => $invoice->status,
                    'warning' => 'Invoice already marked as paid'
                ), 'Invoice Already Paid - Skipping Payment Addition');

                // Return success but don't add duplicate payment
                http_response_code(200);
                echo json_encode(array('status' => 'success', 'message' => 'Invoice already paid'));
                return;
            }

            $result = addInvoicePayment(
                $invoiceId,
                $transactionId,
                $amount,
                $fee,
                'lahza' // Use the correct gateway module name
            );

            logTransaction($gatewayParams['name'], array(
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'fee' => $fee,
                'add_payment_result' => $result,
                'status' => $status
            ), 'Payment Added Successfully');

        } catch (Exception $e) {
            logTransaction($gatewayParams['name'], array(
                'invoice_id' => $invoiceId,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'error' => $e->getMessage()
            ), 'Failed to Add Payment');
            throw $e;
        }
    } elseif (in_array($status, $failedStatuses)) {
        // Handle failed payments
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'action' => 'payment_failed'
        ), 'Payment Failed - Transaction Declined');

        // Optionally, you can add the failed transaction to WHMCS logs
        // This helps with tracking failed attempts

    } elseif (in_array($status, $pendingStatuses)) {
        // Handle pending payments
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'action' => 'payment_pending'
        ), 'Payment Pending - Awaiting Completion');

        // For pending payments, we don't mark as paid yet
        // We wait for a success webhook later

    } else {
        // Handle unknown status
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'transaction_id' => $transactionId,
            'status' => $status,
            'all_statuses' => array(
                'success' => $successStatuses,
                'failed' => $failedStatuses,
                'pending' => $pendingStatuses
            )
        ), 'Payment Status Unknown - Unrecognized Status');
    }

    // Return success response
    http_response_code(200);
    echo json_encode(array('status' => 'success', 'message' => 'Webhook processed'));
}

/**
 * Handle return from payment page
 *
 * @param array $gatewayParams Gateway configuration parameters
 */
function handleReturn($gatewayParams)
{
    global $CONFIG;

    // Get transaction reference from URL - try multiple parameter names
    $reference = '';
    if (isset($_GET['reference']) && !empty($_GET['reference'])) {
        $reference = $_GET['reference'];
    } elseif (isset($_GET['trxref']) && !empty($_GET['trxref'])) {
        $reference = $_GET['trxref'];
    }

    // Log all GET parameters for debugging
    logTransaction($gatewayParams['name'], array(
        'all_get_params' => $_GET,
        'found_reference' => $reference
    ), 'Return Handler - Processing GET Request');

    if (empty($reference)) {
        logTransaction($gatewayParams['name'], array(
            'error' => 'Missing transaction reference',
            'get_params' => $_GET
        ), 'Return Handler Error - No Reference Found');
        throw new Exception('Missing transaction reference');
    }

    // Extract invoice ID from reference (support multiple formats)
    $invoiceId = null;
    if (preg_match('/^INV-(\d+)-\d+-[a-f0-9]{8}$/', $reference, $matches)) {
        // New unique format: INV-123-1234567890-abcd1234
        $invoiceId = (int)$matches[1];
        logTransaction($gatewayParams['name'], array(
            'reference' => $reference,
            'format' => 'unique',
            'invoice_id' => $invoiceId
        ), 'Return Handler - Parsed Unique Format Reference');
    } elseif (preg_match('/^INV-(\d+)$/', $reference, $matches)) {
        // Simple format: INV-123
        $invoiceId = (int)$matches[1];
        logTransaction($gatewayParams['name'], array(
            'reference' => $reference,
            'format' => 'simple',
            'invoice_id' => $invoiceId
        ), 'Return Handler - Parsed Simple Format Reference');
    } elseif (is_numeric($reference)) {
        // Pure numeric format (legacy)
        $invoiceId = (int)$reference;
        logTransaction($gatewayParams['name'], array(
            'reference' => $reference,
            'format' => 'numeric',
            'invoice_id' => $invoiceId
        ), 'Return Handler - Parsed Numeric Format Reference');
    }

    if (empty($invoiceId)) {
        logTransaction($gatewayParams['name'], array(
            'reference' => $reference,
            'error' => 'Could not extract invoice ID from reference'
        ), 'Return Handler Error - Invalid Reference Format');
        throw new Exception('Invalid transaction reference format: ' . $reference);
    }

    // Verify invoice exists
    try {
        $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("SELECT id, status, userid FROM tblinvoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch();

        if (!$invoice) {
            logTransaction($gatewayParams['name'], array(
                'invoice_id' => $invoiceId,
                'error' => 'Invoice not found in database'
            ), 'Return Handler Error - Invoice Not Found');
            throw new Exception('Invoice not found: ' . $invoiceId);
        }

        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'invoice_status' => $invoice['status'],
            'user_id' => $invoice['userid']
        ), 'Return Handler - Invoice Verified');

    } catch (Exception $e) {
        logTransaction($gatewayParams['name'], array(
            'invoice_id' => $invoiceId,
            'error' => $e->getMessage()
        ), 'Return Handler Error - Database Error');
        throw $e;
    }

    // Get system URL from WHMCS configuration with multiple fallbacks
    $systemUrl = '';

    // Method 1: From WHMCS config
    if (!empty($CONFIG['SystemURL'])) {
        $systemUrl = rtrim($CONFIG['SystemURL'], '/');
        logTransaction($gatewayParams['name'], array(
            'method' => 'config',
            'system_url' => $systemUrl
        ), 'Return Handler - Using Config SystemURL');
    }

    // Method 2: Construct from server variables
    if (empty($systemUrl)) {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $systemUrl = $protocol . $_SERVER['HTTP_HOST'];

        // Add path if we're in a subdirectory
        $scriptPath = dirname($_SERVER['SCRIPT_NAME']);
        if ($scriptPath !== '/' && strpos($scriptPath, '/modules/gateways/callback') !== false) {
            // Remove the callback path to get to WHMCS root
            $whmcsPath = str_replace('/modules/gateways/callback', '', $scriptPath);
            $systemUrl .= $whmcsPath;
        }

        logTransaction($gatewayParams['name'], array(
            'method' => 'server_vars',
            'system_url' => $systemUrl,
            'protocol' => $protocol,
            'host' => $_SERVER['HTTP_HOST'],
            'script_path' => $scriptPath
        ), 'Return Handler - Constructed SystemURL from Server');
    }

    // Method 3: Fallback
    if (empty($systemUrl)) {
        $systemUrl = 'http://localhost/Whmcs';
        logTransaction($gatewayParams['name'], array(
            'method' => 'fallback',
            'system_url' => $systemUrl
        ), 'Return Handler - Using Fallback SystemURL');
    }

    $redirectUrl = $systemUrl . '/viewinvoice.php?id=' . $invoiceId;

    // Log the redirect for debugging
    logTransaction($gatewayParams['name'], array(
        'reference' => $reference,
        'invoice_id' => $invoiceId,
        'system_url' => $systemUrl,
        'redirect_url' => $redirectUrl,
        'config_system_url' => $CONFIG['SystemURL'] ?? 'not_set'
    ), 'Return Handler - Redirecting to Invoice');

    // Redirect to invoice
    header('Location: ' . $redirectUrl);
    exit;
}