@echo off
title Lahza Payment Gateway - Ready to Start
color 0A

echo.
echo  ██╗      █████╗ ██╗  ██╗███████╗ █████╗ 
echo  ██║     ██╔══██╗██║  ██║╚══███╔╝██╔══██╗
echo  ██║     ███████║███████║  ███╔╝ ███████║
echo  ██║     ██╔══██║██╔══██║ ███╔╝  ██╔══██║
echo  ███████╗██║  ██║██║  ██║███████╗██║  ██║
echo  ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
echo.
echo  Payment Gateway - Auto Setup with Your Token
echo  ============================================
echo.

REM Your ngrok auth token (already configured)
set NGROK_TOKEN=*************************************************

echo [INFO] Starting Lahza Payment Gateway with your pre-configured token...
echo.

REM Step 1: Check XAMPP
echo [STEP 1] Checking XAMPP Status...
echo.

netstat -an | find "80" | find "LISTENING" >nul
if %errorlevel% neq 0 (
    echo [WARNING] Apache not running on port 80
    echo [INFO] Starting XAMPP Control Panel...
    
    if exist "C:\xampp\xampp-control.exe" (
        start "" "C:\xampp\xampp-control.exe"
        echo.
        echo Please start Apache and MySQL services
        echo Then press any key to continue...
        pause >nul
    ) else (
        echo [ERROR] XAMPP not found. Please start your web server manually.
        echo Make sure it's running on port 80
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] Apache is running on port 80
)

echo.

REM Step 2: Check and setup ngrok
echo [STEP 2] Setting up ngrok with your token...
echo.

where ngrok >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] ngrok is not installed
    echo.
    echo Please install ngrok:
    echo 1. Download from: https://ngrok.com/download
    echo 2. Extract to a folder in your PATH
    echo.
    start https://ngrok.com/download
    pause
    exit /b 1
) else (
    echo [SUCCESS] ngrok found
)

REM Configure ngrok with your token
echo [INFO] Configuring ngrok with your auth token...
ngrok config add-authtoken %NGROK_TOKEN%

if %errorlevel% equ 0 (
    echo [SUCCESS] Auth token configured successfully
) else (
    echo [ERROR] Failed to configure auth token
    pause
    exit /b 1
)

echo.

REM Step 3: Start ngrok tunnel
echo [STEP 3] Starting ngrok tunnel...
echo.

echo [INFO] Starting ngrok tunnel for port 80...
echo [INFO] This will create a public URL for your localhost...
echo.

start "ngrok-tunnel" ngrok http 80

echo [INFO] Waiting for ngrok to initialize...
timeout /t 8 /nobreak >nul

REM Step 4: Get ngrok URL and open interfaces
echo [STEP 4] Opening management interfaces...
echo.

echo [INFO] Opening ngrok web interface...
start http://localhost:4040

timeout /t 3 /nobreak >nul

echo [INFO] Opening Lahza webhook updater...
start http://localhost/Whmcs/update_lahza_webhook.php

timeout /t 2 /nobreak >nul

echo [INFO] Opening WHMCS admin panel...
start http://localhost/Whmcs/admin/

timeout /t 2 /nobreak >nul

echo [INFO] Opening test suite...
start http://localhost/Whmcs/final_test_suite.php

echo.

REM Step 5: Display instructions
echo ========================================
echo    SETUP COMPLETE - FOLLOW THESE STEPS
echo ========================================
echo.
echo 1. CHECK NGROK URL:
echo    - Look at the "Lahza Webhook Updater" tab
echo    - Copy the webhook URL (starts with https://)
echo.
echo 2. UPDATE LAHZA DASHBOARD:
echo    - Go to: https://dashboard.lahza.io
echo    - Login to your account
echo    - Find Webhook/API settings
echo    - Paste the webhook URL from step 1
echo    - Enable events: charge.success, charge.failed
echo    - Save settings
echo.
echo 3. TEST THE PAYMENT:
echo    - Create new invoice in WHMCS
echo    - Set payment method to "Lahza"
echo    - Use test card: ****************
echo    - CVV: 004, Expiry: 03/30
echo    - Complete payment
echo.
echo 4. VERIFY SUCCESS:
echo    - Invoice status should change to "Paid"
echo    - Check ngrok interface for webhook delivery
echo    - Check WHMCS Gateway Logs
echo.
echo ========================================
echo    MONITORING INTERFACES
echo ========================================
echo.
echo - ngrok Interface: http://localhost:4040
echo - Webhook Updater: http://localhost/Whmcs/update_lahza_webhook.php
echo - WHMCS Admin: http://localhost/Whmcs/admin/
echo - Test Suite: http://localhost/Whmcs/final_test_suite.php
echo - Lahza Dashboard: https://dashboard.lahza.io
echo.
echo ========================================
echo    TUNNEL IS ACTIVE
echo ========================================
echo.
echo Your ngrok tunnel is now running!
echo Keep this window open while testing.
echo.
echo Press any key to open Lahza dashboard...
pause >nul

echo [INFO] Opening Lahza dashboard...
start https://dashboard.lahza.io

echo.
echo [SUCCESS] All systems ready!
echo [INFO] Monitor the ngrok interface to see webhook deliveries
echo [INFO] Press Ctrl+C to stop the tunnel when done
echo.

REM Keep monitoring
:monitor_loop
timeout /t 60 /nobreak >nul
echo [INFO] System running... Tunnel active (Press Ctrl+C to stop)
goto monitor_loop
