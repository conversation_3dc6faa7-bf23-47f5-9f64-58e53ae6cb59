# Lahza.io Payment Gateway for WHMCS

## Overview

Complete payment gateway integration for Lahza.io with WHMCS, featuring modern UI/UX, comprehensive security, and full API support.

## Features

### 🚀 Core Features
- **Complete API Integration**: Full Lahza.io API v2024 support
- **Multi-Currency Support**: USD, ILS, JOD
- **Payment Methods**: Cards, Bank Transfers, Mobile Money, QR Codes, USSD
- **Webhook Support**: Real-time payment notifications with signature verification
- **Security**: SSL encryption, IP whitelisting, comprehensive validation

### 🎨 UI/UX Features
- **Modern Interface**: Responsive, mobile-optimized payment forms
- **RTL Support**: Arabic, Hebrew, Farsi language support
- **Dark Mode**: Automatic theme detection
- **Template Integration**: Optimized for WIDDX template
- **Custom Styling**: Configurable CSS customization

### 🔧 Developer Features
- **Comprehensive Logging**: Detailed transaction and error logging
- **Debug Mode**: Enhanced debugging capabilities
- **Card Type Detection**: Display card brand and bank information
- **Error Handling**: User-friendly error messages with retry mechanisms
- **Metadata Support**: Custom transaction data

## Requirements

### System Requirements
- **WHMCS**: 8.0.0 or higher
- **PHP**: 7.4.0 or higher
- **SSL**: Required for production
- **Extensions**: curl, json, openssl, mbstring

### Lahza.io Account
- Active Lahza.io merchant account
- API keys (test and live)
- Webhook configuration

## Installation

### 1. File Structure
```
modules/gateways/
├── lahza.php                    # Main gateway module
├── lahza/
│   ├── whmcs.json              # Module metadata
│   ├── README.md               # This file
│   ├── CHANGELOG.md            # Version history
│   ├── TROUBLESHOOTING.md      # Troubleshooting guide
│   └── logo.png                # Gateway logo
└── callback/
    └── lahza.php               # Webhook handler
```

### 2. Gateway Configuration
1. Navigate to **Setup > Payments > Payment Gateways**
2. Find and activate **Lahza.io Payment Gateway**
3. Configure the following settings:

#### Required Settings
- **Public Key**: Your Lahza.io public key (pk_test_... or pk_live_...)
- **Secret Key**: Your Lahza.io secret key (sk_test_... or sk_live_...)

#### Optional Settings
- **Test Mode**: Enable for testing (default: enabled)
- **Payment Method**: Popup (recommended) or Redirect
- **Payment Channels**: Comma-separated list of allowed channels
- **Enable Logging**: Enable detailed logging (recommended)
- **Show Card Type**: Display card information in logs
- **Custom CSS**: Additional styling
- **IP Whitelist**: Lahza.io webhook IP addresses

### 3. Webhook Configuration
1. Login to your Lahza.io dashboard
2. Navigate to **Settings > Webhooks**
3. Add webhook URL: `https://yourdomain.com/modules/gateways/callback/lahza.php`
4. Select events: `charge.success`, `refund.processed`
5. Save webhook secret for WHMCS configuration

## Configuration Options

### Payment Channels
Configure allowed payment methods:
```
card,bank,mobile_money,qr,ussd,bank_transfer
```

### Supported Currencies
- **USD**: US Dollar
- **ILS**: Israeli Shekel  
- **JOD**: Jordanian Dinar

### Payment Methods
- **Cards**: Visa, Mastercard, American Express
- **Bank Transfers**: Direct bank integration
- **Mobile Money**: Mobile wallet payments
- **QR Codes**: Quick payment via QR scanning
- **USSD**: USSD-based payments

## API Integration

### Endpoints Used
- `POST /transaction/initialize` - Initialize payment
- `GET /transaction/verify/{reference}` - Verify payment
- `POST /transaction/refund` - Process refunds

### Webhook Events
- `charge.success` - Payment completed successfully
- `charge.failed` - Payment failed
- `refund.processed` - Refund completed

### Metadata Structure
```json
{
  "invoice_id": "12345",
  "client_id": "67890",
  "company_name": "Your Company",
  "whmcs_url": "https://yourdomain.com",
  "payment_method": "lahza",
  "client_name": "John Doe",
  "custom_fields": [
    {
      "display_name": "Invoice ID",
      "variable_name": "invoice_id",
      "value": "12345"
    }
  ]
}
```

## Security Features

### Webhook Security
- **Signature Verification**: HMAC-SHA256 signature validation
- **IP Whitelisting**: Restrict webhook sources to Lahza.io IPs
- **SSL Encryption**: All communications encrypted with SSL/TLS

### Data Protection
- **PCI Compliance**: No sensitive card data stored locally
- **Tokenization**: Secure token-based transactions
- **Audit Logging**: Comprehensive transaction logging

## Template Integration

### WIDDX Template
Complete integration with WIDDX template including:
- Responsive payment forms
- RTL language support
- Dark mode compatibility
- Custom styling options

### Order Forms
Compatible with all WHMCS order forms:
- Standard Cart
- Modern Cart
- Comparison Tables
- Custom Order Forms

## Troubleshooting

### Common Issues

#### Payment Not Processing
- Check SSL certificate validity
- Verify API keys are correct
- Ensure webhook URL is accessible
- Check browser console for JavaScript errors

#### Webhook Not Received
- Verify webhook URL configuration
- Check IP whitelist settings
- Validate webhook signature
- Review server logs

#### Currency Not Supported
- Ensure currency is USD, ILS, or JOD
- Check WHMCS currency configuration
- Verify exchange rates are set

### Debug Mode
Enable logging in gateway configuration to see detailed information:
- **Module Log**: Setup > Logs > Module Log
- **Gateway Log**: Setup > Logs > Gateway Log
- **Activity Log**: Setup > Logs > Activity Log

### Log Locations
- **WHMCS Logs**: `/storage/logs/`
- **Server Logs**: `/var/log/apache2/error.log` or `/var/log/nginx/error.log`
- **PHP Logs**: `/var/log/php_errors.log`

## Testing

### Test Mode
1. Enable test mode in gateway configuration
2. Use test API keys from Lahza.io dashboard
3. Use test card numbers provided by Lahza.io

### Test Cards
```
Visa Success: ****************
Visa Decline: ****************
Mastercard Success: ****************
Mastercard Decline: ****************
```

### Test Scenarios
- ✅ Successful payment
- ❌ Failed payment (insufficient funds)
- 🔄 Cancelled payment
- 📱 Mobile payment
- 🏦 Bank transfer
- 💳 Different card types

## Support

### Documentation
- [Lahza.io API Documentation](https://docs.lahza.io/)
- [WHMCS Payment Gateway Documentation](https://docs.whmcs.com/Payment_Gateways)

### Contact Support
- **Lahza.io Support**: <EMAIL>
- **WHMCS Support**: https://www.whmcs.com/support/

### Community
- WHMCS Community Forums
- Lahza.io Developer Community

## Changelog

### Version 3.0.0 (Current)
- Complete WHMCS 8.x compatibility
- Enhanced security features
- Improved error handling
- RTL language support
- Dark mode support
- Mobile optimization
- Comprehensive logging

### Version 2.0.0
- Webhook signature verification
- Card type detection
- Multi-currency support
- Enhanced logging

### Version 1.0.0
- Initial release
- Basic payment processing
- API integration

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**Last Updated**: January 2025  
**Version**: 3.0.0  
**Compatibility**: WHMCS 8.x, Lahza.io API v2024