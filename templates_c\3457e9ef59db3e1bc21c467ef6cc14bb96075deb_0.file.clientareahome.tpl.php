<?php
/* Smarty version 3.1.48, created on 2025-06-18 00:44:46
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\clientareahome.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_6851efdecdb408_69290570',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '3457e9ef59db3e1bc21c467ef6cc14bb96075deb' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\clientareahome.tpl',
      1 => 1747864397,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6851efdecdb408_69290570 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->smarty->ext->_tplFunction->registerTplFunctions($_smarty_tpl, array (
  'outputHomePanels' => 
  array (
    'compiled_filepath' => 'C:\\xampp\\htdocs\\Whmcs\\templates_c\\3457e9ef59db3e1bc21c467ef6cc14bb96075deb_0.file.clientareahome.tpl.php',
    'uid' => '3457e9ef59db3e1bc21c467ef6cc14bb96075deb',
    'call_name' => 'smarty_template_function_outputHomePanels_19675835316851efdeb2b3a1_46915583',
  ),
));
$_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/flashmessage.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

<div class="dashboard-stats-container mb-4">
    <div class="row g-3">
        <!-- Services Card -->
        <div class="col-sm-6 col-xl-3">
            <div class="dashboard-stat-card services-card">
                <a href="clientarea.php?action=services" class="card-link">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-3 p-xl-4">
                            <div class="d-flex align-items-center mb-3 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                <div class="stat-icon-circle bg-primary-soft <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
                                    <i class="fas fa-cube text-primary"></i>
                                </div>
                                <h6 class="card-subtitle mb-0 text-muted"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'navservices'),$_smarty_tpl ) );?>
</h6>
                            </div>
                            <div class="d-flex align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                <h2 class="stat-value mb-0 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-2<?php } else { ?>me-2<?php }?>" style="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>margin-top: 20px; font-size: 40px; line-height: 1; direction: ltr;<?php }?>"><?php echo $_smarty_tpl->tpl_vars['clientsstats']->value['productsnumactive'];?>
</h2>
                                <span class="badge bg-primary-soft text-primary">Active</span>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="progress" style="height: 4px;">
                                <div class="progress-bar bg-primary" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <?php if ($_smarty_tpl->tpl_vars['clientsstats']->value['numdomains'] || $_smarty_tpl->tpl_vars['registerdomainenabled']->value || $_smarty_tpl->tpl_vars['transferdomainenabled']->value) {?>
            <!-- Domains Card -->
            <div class="col-sm-6 col-xl-3">
                <div class="dashboard-stat-card domains-card">
                    <a href="clientarea.php?action=domains" class="card-link">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body p-3 p-xl-4">
                                <div class="d-flex align-items-center mb-3 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                    <div class="stat-icon-circle bg-success-soft <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
                                        <i class="fas fa-globe text-success"></i>
                                    </div>
                                    <h6 class="card-subtitle mb-0 text-muted"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'navdomains'),$_smarty_tpl ) );?>
</h6>
                                </div>
                                <div class="d-flex align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                    <h2 class="stat-value mb-0 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-2<?php } else { ?>me-2<?php }?>" style="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>margin-top: 20px; font-size: 40px; line-height: 1; direction: ltr;<?php }?>"><?php echo $_smarty_tpl->tpl_vars['clientsstats']->value['numactivedomains'];?>
</h2>
                                    <span class="badge bg-success-soft text-success">Active</span>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent border-0 pt-0">
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        <?php } elseif ($_smarty_tpl->tpl_vars['condlinks']->value['affiliates'] && $_smarty_tpl->tpl_vars['clientsstats']->value['isAffiliate']) {?>
            <!-- Affiliates Card -->
            <div class="col-sm-6 col-xl-3">
                <div class="dashboard-stat-card affiliates-card">
                    <a href="affiliates.php" class="card-link">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body p-3 p-xl-4">
                                <div class="d-flex align-items-center mb-3 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                    <div class="stat-icon-circle bg-success-soft <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
                                        <i class="fas fa-shopping-cart text-success"></i>
                                    </div>
                                    <h6 class="card-subtitle mb-0 text-muted"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'affiliatessignups'),$_smarty_tpl ) );?>
</h6>
                                </div>
                                <div class="d-flex align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                    <h2 class="stat-value mb-0 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-2<?php } else { ?>me-2<?php }?>" style="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>margin-top: 20px; font-size: 40px; line-height: 1; direction: ltr;<?php }?>"><?php echo $_smarty_tpl->tpl_vars['clientsstats']->value['numaffiliatesignups'];?>
</h2>
                                    <span class="badge bg-success-soft text-success">Signups</span>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent border-0 pt-0">
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        <?php } else { ?>
            <!-- Quotes Card -->
            <div class="col-sm-6 col-xl-3">
                <div class="dashboard-stat-card quotes-card">
                    <a href="clientarea.php?action=quotes" class="card-link">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body p-3 p-xl-4">
                                <div class="d-flex align-items-center mb-3 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                    <div class="stat-icon-circle bg-success-soft <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
                                        <i class="far fa-file-alt text-success"></i>
                                    </div>
                                    <h6 class="card-subtitle mb-0 text-muted"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'quotes'),$_smarty_tpl ) );?>
</h6>
                                </div>
                                <div class="d-flex align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                    <h2 class="stat-value mb-0 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-2<?php } else { ?>me-2<?php }?>" style="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>margin-top: 20px; font-size: 40px; line-height: 1; direction: ltr;<?php }?>"><?php echo $_smarty_tpl->tpl_vars['clientsstats']->value['numquotes'];?>
</h2>
                                    <span class="badge bg-success-soft text-success">Active</span>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent border-0 pt-0">
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        <?php }?>

        <!-- Tickets Card -->
        <div class="col-sm-6 col-xl-3">
            <div class="dashboard-stat-card tickets-card">
                <a href="supporttickets.php" class="card-link">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-3 p-xl-4">
                            <div class="d-flex align-items-center mb-3 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                <div class="stat-icon-circle bg-danger-soft <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
                                    <i class="fas fa-comments text-danger"></i>
                                </div>
                                <h6 class="card-subtitle mb-0 text-muted"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'navtickets'),$_smarty_tpl ) );?>
</h6>
                            </div>
                            <div class="d-flex align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                <h2 class="stat-value mb-0 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-2<?php } else { ?>me-2<?php }?>" style="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>margin-top: 20px; font-size: 40px; line-height: 1; direction: ltr;<?php }?>"><?php echo $_smarty_tpl->tpl_vars['clientsstats']->value['numactivetickets'];?>
</h2>
                                <span class="badge bg-danger-soft text-danger">Open</span>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="progress" style="height: 4px;">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Invoices Card -->
        <div class="col-sm-6 col-xl-3">
            <div class="dashboard-stat-card invoices-card">
                <a href="clientarea.php?action=invoices" class="card-link">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body p-3 p-xl-4">
                            <div class="d-flex align-items-center mb-3 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                <div class="stat-icon-circle bg-warning-soft <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
                                    <i class="fas fa-credit-card text-warning"></i>
                                </div>
                                <h6 class="card-subtitle mb-0 text-muted"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'navinvoices'),$_smarty_tpl ) );?>
</h6>
                            </div>
                            <div class="d-flex align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                                <h2 class="stat-value mb-0 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-2<?php } else { ?>me-2<?php }?>" style="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>margin-top: 20px; font-size: 40px; line-height: 1; direction: ltr;<?php }?>"><?php echo $_smarty_tpl->tpl_vars['clientsstats']->value['numunpaidinvoices'];?>
</h2>
                                <span class="badge bg-warning-soft text-warning">Unpaid</span>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="progress" style="height: 4px;">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['addons_html']->value, 'addon_html');
$_smarty_tpl->tpl_vars['addon_html']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['addon_html']->value) {
$_smarty_tpl->tpl_vars['addon_html']->do_else = false;
?>
    <div>
        <?php echo $_smarty_tpl->tpl_vars['addon_html']->value;?>

    </div>
<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

<div class="client-home-panels mt-4">
    <div class="row">
        <div class="col-12">
            

            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['panels']->value, 'item');
$_smarty_tpl->tpl_vars['item']->iteration = 0;
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
$_smarty_tpl->tpl_vars['item']->iteration++;
$__foreach_item_12_saved = $_smarty_tpl->tpl_vars['item'];
?>
                <?php if ($_smarty_tpl->tpl_vars['item']->value->getExtra('colspan')) {?>
                    <?php $_smarty_tpl->smarty->ext->_tplFunction->callTemplateFunction($_smarty_tpl, 'outputHomePanels', array(), true);?>

                    <?php $_smarty_tpl->_assignInScope('panels', $_smarty_tpl->tpl_vars['panels']->value->removeChild($_smarty_tpl->tpl_vars['item']->value->getName()));?>
                <?php }?>
            <?php
$_smarty_tpl->tpl_vars['item'] = $__foreach_item_12_saved;
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

        </div>
        <div class="col-md-6 col-lg-12 col-xl-6">

            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['panels']->value, 'item');
$_smarty_tpl->tpl_vars['item']->iteration = 0;
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
$_smarty_tpl->tpl_vars['item']->iteration++;
$__foreach_item_13_saved = $_smarty_tpl->tpl_vars['item'];
?>
                <?php if ((1 & $_smarty_tpl->tpl_vars['item']->iteration)) {?>
                    <?php $_smarty_tpl->smarty->ext->_tplFunction->callTemplateFunction($_smarty_tpl, 'outputHomePanels', array(), true);?>

                <?php }?>
            <?php
$_smarty_tpl->tpl_vars['item'] = $__foreach_item_13_saved;
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

        </div>
        <div class="col-md-6 col-lg-12 col-xl-6">

            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['panels']->value, 'item');
$_smarty_tpl->tpl_vars['item']->iteration = 0;
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
$_smarty_tpl->tpl_vars['item']->iteration++;
$__foreach_item_14_saved = $_smarty_tpl->tpl_vars['item'];
?>
                <?php if (!(1 & $_smarty_tpl->tpl_vars['item']->iteration)) {?>
                    <?php $_smarty_tpl->smarty->ext->_tplFunction->callTemplateFunction($_smarty_tpl, 'outputHomePanels', array(), true);?>

                <?php }?>
            <?php
$_smarty_tpl->tpl_vars['item'] = $__foreach_item_14_saved;
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

        </div>
    </div>
</div>
<?php }
/* smarty_template_function_outputHomePanels_19675835316851efdeb2b3a1_46915583 */
if (!function_exists('smarty_template_function_outputHomePanels_19675835316851efdeb2b3a1_46915583')) {
function smarty_template_function_outputHomePanels_19675835316851efdeb2b3a1_46915583(Smarty_Internal_Template $_smarty_tpl,$params) {
foreach ($params as $key => $value) {
$_smarty_tpl->tpl_vars[$key] = new Smarty_Variable($value, $_smarty_tpl->isRenderingCache);
}
?>

                <div menuItemName="<?php echo $_smarty_tpl->tpl_vars['item']->value->getName();?>
" class="card card-accent-<?php echo $_smarty_tpl->tpl_vars['item']->value->getExtra('color');?>
 shadow-sm border-0 mb-4 <?php if ($_smarty_tpl->tpl_vars['item']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['item']->value->getClass();
}?>"<?php if ($_smarty_tpl->tpl_vars['item']->value->getAttribute('id')) {?> id="<?php echo $_smarty_tpl->tpl_vars['item']->value->getAttribute('id');?>
"<?php }?>>
                    <div class="card-header bg-transparent border-bottom-0" style="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>display: flex; flex-direction: row-reverse; justify-content: space-between; direction: ltr;<?php }?>">
                        <h3 class="card-title m-0 d-flex align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>">
                            <?php if ($_smarty_tpl->tpl_vars['item']->value->hasIcon()) {?>
                                <div class="panel-icon-circle bg-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
-soft <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
                                    <i class="<?php echo $_smarty_tpl->tpl_vars['item']->value->getIcon();?>
 text-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
"></i>
                                </div>
                            <?php }?>
                            <span><?php echo $_smarty_tpl->tpl_vars['item']->value->getLabel();?>
</span>
                            <?php if ($_smarty_tpl->tpl_vars['item']->value->hasBadge()) {?>
                                <span class="badge bg-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
-soft text-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-2<?php } else { ?>ms-2<?php }?>"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
</span>
                            <?php }?>

                            <?php if ($_smarty_tpl->tpl_vars['item']->value->getExtra('btn-link') && $_smarty_tpl->tpl_vars['item']->value->getExtra('btn-text')) {?>
                                <div class="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-auto<?php } else { ?>ms-auto<?php }?>">
                                    <a href="<?php echo $_smarty_tpl->tpl_vars['item']->value->getExtra('btn-link');?>
" class="btn btn-sm btn-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
">
                                        <?php if ($_smarty_tpl->tpl_vars['item']->value->getExtra('btn-icon')) {?><i class="<?php echo $_smarty_tpl->tpl_vars['item']->value->getExtra('btn-icon');?>
"></i> <?php }?>
                                        <?php echo $_smarty_tpl->tpl_vars['item']->value->getExtra('btn-text');?>

                                    </a>
                                </div>
                            <?php }?>
                        </h3>
                    </div>
                    <?php if ($_smarty_tpl->tpl_vars['item']->value->hasBodyHtml()) {?>
                        <div class="card-body">
                            <?php echo $_smarty_tpl->tpl_vars['item']->value->getBodyHtml();?>

                        </div>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>
                        <div class="list-group list-group-flush<?php if ($_smarty_tpl->tpl_vars['item']->value->getChildrenAttribute('class')) {?> <?php echo $_smarty_tpl->tpl_vars['item']->value->getChildrenAttribute('class');
}?>">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['item']->value->getChildren(), 'childItem');
$_smarty_tpl->tpl_vars['childItem']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['childItem']->value) {
$_smarty_tpl->tpl_vars['childItem']->do_else = false;
?>
                                <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getUri()) {?>
                                    <a menuItemName="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getName();?>
" href="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getUri();?>
" class="list-group-item list-group-item-action border-0 d-flex align-items-center<?php if ($_smarty_tpl->tpl_vars['childItem']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getClass();
}
if ($_smarty_tpl->tpl_vars['childItem']->value->isCurrent()) {?> active<?php }?> <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>"<?php if ($_smarty_tpl->tpl_vars['childItem']->value->getAttribute('dataToggleTab')) {?> data-toggle="tab"<?php }
if ($_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target')) {?> target="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target');?>
"<?php }?> id="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getId();?>
">
                                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasIcon()) {?>
                                            <i class="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getIcon();?>
 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>"></i>
                                        <?php }?>
                                        <span><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>
</span>
                                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasBadge()) {?>
                                            <span class="badge bg-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
-soft text-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-2<?php } else { ?>ms-2<?php }?> <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-auto<?php } else { ?>ms-auto<?php }?>"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
</span>
                                        <?php }?>
                                    </a>
                                <?php } else { ?>
                                    <div menuItemName="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getName();?>
" class="list-group-item list-group-item-action border-0 d-flex align-items-center<?php if ($_smarty_tpl->tpl_vars['childItem']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getClass();
}?> <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>flex-row-reverse<?php }?>" id="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getId();?>
">
                                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasIcon()) {?>
                                            <i class="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getIcon();?>
 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>"></i>
                                        <?php }?>
                                        <span><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>
</span>
                                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasBadge()) {?>
                                            <span class="badge bg-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
-soft text-<?php echo (($tmp = @$_smarty_tpl->tpl_vars['item']->value->getExtra('color'))===null||$tmp==='' ? 'primary' : $tmp);?>
 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-2<?php } else { ?>ms-2<?php }?> <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-auto<?php } else { ?>ms-auto<?php }?>"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
</span>
                                        <?php }?>
                                    </div>
                                <?php }?>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </div>
                    <?php }?>
                    <?php if ($_smarty_tpl->tpl_vars['item']->value->hasFooterHtml()) {?>
                        <div class="card-footer bg-transparent">
                            <?php echo $_smarty_tpl->tpl_vars['item']->value->getFooterHtml();?>

                        </div>
                    <?php }?>
                </div>
            <?php
}}
/*/ smarty_template_function_outputHomePanels_19675835316851efdeb2b3a1_46915583 */
}
