# Troubleshooting Guide

This guide helps resolve common issues with the Lahza.io Payment Gateway for WHMCS.

## Quick Diagnostics

### 🔍 Pre-flight Checklist

Before troubleshooting, verify these basics:

- [ ] SSL/HTTPS is enabled and working
- [ ] Lahza.io gateway is enabled in WHMCS admin
- [ ] API keys are correctly configured (test vs live)
- [ ] Webhook URL is accessible from the internet
- [ ] JavaScript is enabled in customer's browser
- [ ] No ad-blockers blocking payment scripts
- [ ] WHMCS version 8.0.0 or higher
- [ ] PHP version 7.4.0 or higher

## Common Issues

### 1. Payment Button Not Working

#### Symptoms
- Payment button doesn't respond to clicks
- No popup or redirect occurs
- JavaScript errors in browser console

#### Diagnosis
```javascript
// Open browser console (F12) and check for errors
console.log('Lahza payment data:', window.lahzaPaymentData);
console.log('Lahza handler:', window.lahzaPaymentHandler);
```

#### Solutions

**A. JavaScript Library Not Loaded**
```html
<!-- Verify this script is loaded -->
<script src="https://js.lahza.io/inline.min.js"></script>
```

**B. Payment Data Missing**
```javascript
// Check if payment data is initialized
if (!window.lahzaPaymentData) {
    console.error('Lahza payment data not initialized');
}
```

**C. Handler Not Initialized**
```javascript
// Manually initialize if needed
if (!window.lahzaPaymentHandler) {
    window.lahzaPaymentHandler = new LahzaPaymentHandler({
        enableLogging: true
    });
}
```

**D. API Key Issues**
- Verify public key format: `pk_test_...` or `pk_live_...`
- Ensure test/live mode matches key type
- Check for extra spaces or characters

### 2. Webhook Not Received

#### Symptoms
- Payment successful in Lahza.io but not marked as paid in WHMCS
- No transaction record in WHMCS logs
- Invoice remains unpaid

#### Diagnosis
```bash
# Test webhook URL accessibility
curl -X POST https://yourdomain.com/modules/gateways/callback/lahza.php \
  -H "Content-Type: application/json" \
  -H "X-Lahza-Signature: test_signature" \
  -d '{"test": "webhook"}'
```

#### Solutions

**A. Webhook URL Not Accessible**
- Ensure URL is publicly accessible (not localhost)
- Check firewall settings
- Verify SSL certificate is valid
- Test from external service (e.g., webhook.site)

**B. IP Whitelist Issues**
```php
// In gateway configuration
'ipWhitelist' => '*************,**************'
```

**C. Webhook Signature Verification**
- Check webhook secret in Lahza.io dashboard
- Verify signature verification is working
- Enable test mode for debugging

**D. Server Configuration**
```apache
# .htaccess - Ensure POST requests are allowed
<Files "lahza.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>
```

### 3. Invalid Signature Error

#### Symptoms
- HTTP 401 responses from webhook
- "Invalid signature" error messages
- Webhook requests being rejected

#### Diagnosis
```php
// Check webhook signature verification
$payload = file_get_contents('php://input');
$signature = $_SERVER['HTTP_X_LAHZA_SIGNATURE'] ?? '';
$secretKey = 'your_secret_key';

$expectedSignature = hash_hmac('sha256', $payload, $secretKey);
echo "Expected: " . $expectedSignature . "\n";
echo "Received: " . str_replace('sha256=', '', $signature) . "\n";
```

#### Solutions

**A. Incorrect Secret Key**
- Verify secret key in WHMCS matches Lahza.io dashboard
- Check for extra spaces or characters
- Ensure test/live keys match environment

**B. Signature Format Issues**
- Lahza.io sends signature as `sha256=hash`
- Code should handle both formats: `sha256=hash` and `hash`

**C. Test Mode Configuration**
```php
// For testing, allow test signatures
if ($testMode && $signature === 'test_signature') {
    // Allow test signature in test mode
}
```

### 4. Currency Not Supported

#### Symptoms
- Error message about unsupported currency
- Payment form doesn't load
- Currency conversion errors

#### Diagnosis
```php
// Check supported currencies
$supportedCurrencies = ['ILS', 'USD', 'JOD'];
$currentCurrency = $params['currency'];
if (!in_array($currentCurrency, $supportedCurrencies)) {
    echo "Currency {$currentCurrency} not supported";
}
```

#### Solutions

**A. Add Currency Support**
1. Go to WHMCS Admin → Setup → Payments → Currencies
2. Ensure USD, ILS, or JOD is configured
3. Set appropriate exchange rates

**B. Currency Conversion**
```php
// Ensure proper currency conversion
$amountInCents = (int)($amount * 100);
```

**C. Default Currency Fallback**
```php
// Set default currency if not supported
if (!in_array($currency, ['ILS', 'USD', 'JOD'])) {
    $currency = 'USD'; // Default fallback
}
```

### 5. Template Integration Issues

#### Symptoms
- Payment form doesn't appear
- Styling is broken
- Template conflicts

#### Diagnosis
```php
// Check template file existence
$templatePath = ROOTDIR . '/templates/widdx/payment/lahza/';
$requiredFiles = [
    'payment-form.tpl',
    'lahza-payment.js',
    'lahza-payment.css'
];

foreach ($requiredFiles as $file) {
    if (!file_exists($templatePath . $file)) {
        echo "Missing: {$file}\n";
    }
}
```

#### Solutions

**A. Template Files Missing**
- Ensure all template files are uploaded
- Check file permissions (644 for files, 755 for directories)
- Verify template path is correct

**B. Template Cache Issues**
```bash
# Clear WHMCS template cache
rm -rf /path/to/whmcs/templates_c/*
```

**C. CSS Conflicts**
```css
/* Add specificity to Lahza styles */
.lahza-payment-container {
    /* Your styles with !important if needed */
    background: #fff !important;
}
```

### 6. API Connection Issues

#### Symptoms
- "Failed to initialize payment" errors
- Timeout errors
- Connection refused errors

#### Diagnosis
```php
// Test API connectivity
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: {$httpCode}\n";
echo "Error: {$error}\n";
echo "Response: {$response}\n";
```

#### Solutions

**A. Firewall Blocking Outbound Connections**
- Whitelist Lahza.io domains and IPs
- Allow HTTPS (port 443) outbound connections
- Check server firewall rules

**B. cURL Configuration Issues**
```php
// Enhanced cURL settings
curl_setopt($ch, CURLOPT_USERAGENT, 'WHMCS-Lahza/3.0');
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
```

**C. SSL Certificate Issues**
```php
// For development only - not for production
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
```

**D. Proxy Configuration**
```php
// If behind corporate proxy
curl_setopt($ch, CURLOPT_PROXY, 'proxy.company.com:8080');
curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'username:password');
```

## Debug Mode

### Enable Comprehensive Logging

**1. Gateway Configuration**
```php
// In WHMCS Admin → Payment Gateways → Lahza.io
'enableLogging' => true
```

**2. JavaScript Debug Mode**
```javascript
// Add to payment page
window.lahzaEnableLogging = true;
```

**3. PHP Error Logging**
```php
// Add to callback file for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', '/path/to/error.log');
```

**4. Webhook Debug Headers**
```php
// Log all webhook headers
$headers = getallheaders();
error_log('Webhook Headers: ' . json_encode($headers));
```

### Log Locations

**WHMCS Logs:**
- Module Log: `Setup → Logs → Module Log`
- Gateway Log: `Setup → Logs → Gateway Log`
- Activity Log: `Setup → Logs → Activity Log`

**Server Logs:**
- Apache: `/var/log/apache2/error.log`
- Nginx: `/var/log/nginx/error.log`
- PHP: `/var/log/php_errors.log`

**Custom Logs:**
- Webhook Log: `/path/to/whmcs/storage/logs/webhook.log`
- Payment Log: `/path/to/whmcs/storage/logs/payment.log`

## Testing Procedures

### 1. Test Mode Setup

```php
// Gateway configuration for testing
$config = [
    'testMode' => true,
    'publicKey' => 'pk_test_your_test_key',
    'secretKey' => 'sk_test_your_test_key',
    'enableLogging' => true
];
```

### 2. Test Payment Flow

**A. Frontend Test**
1. Create test invoice
2. Proceed to payment
3. Select Lahza.io gateway
4. Complete test payment
5. Verify invoice status

**B. Backend Verification**
1. Check WHMCS logs for payment initiation
2. Verify webhook received and processed
3. Confirm invoice marked as paid
4. Review transaction details

### 3. Test Cards

```
Visa Success: ****************
Visa Decline: ****************
Mastercard Success: ****************
Mastercard Decline: 5000000000000009
American Express: ***************
```

### 4. Webhook Testing

```bash
# Test webhook manually
curl -X POST https://yourdomain.com/modules/gateways/callback/lahza.php \
  -H "Content-Type: application/json" \
  -H "X-Lahza-Signature: test_signature" \
  -d '{
    "event": "charge.success",
    "data": {
      "reference": "WHMCS_123_test",
      "status": "success",
      "amount": 10000,
      "currency": "USD",
      "metadata": {
        "invoice_id": "123"
      }
    }
  }'
```

## Performance Optimization

### 1. Frontend Optimization

**A. Lazy Load Payment Scripts**
```javascript
// Load Lahza script only when needed
function loadLahzaScript() {
    if (!document.querySelector('script[src*="lahza.io"]')) {
        const script = document.createElement('script');
        script.src = 'https://js.lahza.io/inline.min.js';
        script.async = true;
        document.head.appendChild(script);
    }
}
```

**B. Cache Static Assets**
```apache
# .htaccess for caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>
```

### 2. Backend Optimization

**A. Connection Pooling**
```php
// Reuse cURL handles
class LahzaAPIClient {
    private static $curlHandle = null;
    
    public static function getCurlHandle() {
        if (self::$curlHandle === null) {
            self::$curlHandle = curl_init();
            // Set common options
        }
        return self::$curlHandle;
    }
}
```

**B. Response Caching**
```php
// Cache successful verifications temporarily
$cacheKey = "lahza_verify_{$reference}";
if (function_exists('cache_get')) {
    $cached = cache_get($cacheKey);
    if ($cached) {
        return $cached;
    }
}
```

## Security Hardening

### 1. Webhook Security

**A. Signature Verification**
```php
// Always verify webhook signatures
if (!lahza_verifyWebhookSignature($payload, $signature, $secretKey)) {
    http_response_code(401);
    die('Invalid signature');
}
```

**B. IP Whitelisting**
```php
// Restrict webhook sources
$allowedIPs = ['*************', '**************'];
$clientIP = $_SERVER['REMOTE_ADDR'];
if (!in_array($clientIP, $allowedIPs)) {
    http_response_code(403);
    die('IP not allowed');
}
```

**C. Rate Limiting**
```php
// Implement basic rate limiting
$rateLimitKey = "webhook_rate_limit_{$clientIP}";
$requests = cache_get($rateLimitKey) ?: 0;
if ($requests > 100) { // 100 requests per hour
    http_response_code(429);
    die('Rate limit exceeded');
}
cache_set($rateLimitKey, $requests + 1, 3600);
```

### 2. API Key Security

**A. Environment Variables**
```php
// Store keys in environment variables
$publicKey = getenv('LAHZA_PUBLIC_KEY');
$secretKey = getenv('LAHZA_SECRET_KEY');
```

**B. Key Validation**
```php
// Validate key formats
if (!preg_match('/^pk_(test_|live_)[a-zA-Z0-9]+$/', $publicKey)) {
    throw new Exception('Invalid public key format');
}
```

## Common Error Messages

### "Payment gateway not configured properly"
**Cause**: Missing or invalid API keys
**Solution**: 
- Check API keys are set in gateway configuration
- Verify key format (pk_test_... or pk_live_...)
- Ensure test/live mode matches key type

### "Invalid payment amount"
**Cause**: Amount validation failed
**Solution**:
- Check amount is greater than 0
- Verify currency conversion is correct
- Ensure proper number formatting

### "Transaction verification failed"
**Cause**: Webhook verification issues
**Solution**:
- Check webhook signature verification
- Verify API connectivity
- Review transaction reference format

### "Unsupported currency"
**Cause**: Currency not supported by Lahza.io
**Solution**:
- Use only USD, ILS, or JOD
- Check WHMCS currency configuration
- Verify exchange rates are set

### "SSL certificate verification failed"
**Cause**: SSL/TLS issues
**Solution**:
- Ensure valid SSL certificate
- Update CA certificates
- Check SSL configuration

## Support Resources

### Documentation
- [Lahza.io API Documentation](https://docs.lahza.io/)
- [WHMCS Payment Gateway Documentation](https://docs.whmcs.com/Payment_Gateways)
- [Gateway README](README.md)
- [Changelog](CHANGELOG.md)

### Contact Support
- **Lahza.io Support**: <EMAIL>
- **WHMCS Support**: https://www.whmcs.com/support/
- **Emergency Issues**: Check status pages first

### Community Resources
- WHMCS Community Forums
- Lahza.io Developer Community
- Stack Overflow (tags: whmcs, lahza)

## Maintenance Checklist

### Daily
- [ ] Monitor payment success rates
- [ ] Check error logs for issues
- [ ] Verify webhook delivery

### Weekly
- [ ] Review transaction logs
- [ ] Test payment flow
- [ ] Check API response times
- [ ] Monitor server resources

### Monthly
- [ ] Update gateway module if available
- [ ] Review security settings
- [ ] Performance optimization review
- [ ] Documentation updates

### Quarterly
- [ ] Full integration testing
- [ ] Security audit
- [ ] Backup configuration
- [ ] API key rotation (if required)

---

**Last Updated**: January 2025  
**Version**: 3.0.0  
**For Technical Support**: Create detailed issue reports with logs and error messages