# 🚀 Lahza Payment Gateway - Quick Start Guide

## 📋 ملخص سريع | Quick Summary

تم إنشاء نظام تشغيل تلقائي لحل مشكلة webhook وتشغيل بوابة الدفع لحظة بشكل كامل.

## 🎯 التشغيل السريع | Quick Start

### الطريقة 1: تشغيل تلقائي (الأسهل)
```bash
# انقر نقرة مزدوجة على الملف
START_LAHZA_TESTING.bat
```

### الطريقة 2: PowerShell (متقدم)
```powershell
# تشغيل PowerShell كمدير
.\Setup-LahzaGateway.ps1
```

### الطريقة 3: يدوي
```bash
# 1. تشغيل ngrok
ngrok http 80

# 2. فتح واجهة التحديث
http://localhost/Whmcs/update_lahza_webhook.php
```

## 📁 الملفات المنشأة | Created Files

### ملفات التشغيل | Execution Files
- `START_LAHZA_TESTING.bat` - تشغيل تلقائي كامل
- `Setup-LahzaGateway.ps1` - سكريبت PowerShell متقدم
- `setup_ngrok_automation.bat` - إعداد ngrok أساسي

### ملفات المساعدة | Helper Files
- `update_lahza_webhook.php` - تحديث webhook URL تلقائياً
- `final_test_suite.php` - مجموعة اختبارات شاملة

### ملفات التوثيق | Documentation Files
- `NGROK_SETUP_GUIDE.md` - دليل إعداد ngrok مفصل
- `WHMCS_LAHZA_ANALYSIS.md` - تحليل شامل للنظام
- `WEBHOOK_TROUBLESHOOTING.md` - دليل حل المشاكل

## 🔧 متطلبات النظام | System Requirements

### البرامج المطلوبة | Required Software
- ✅ XAMPP (Apache + MySQL)
- ✅ ngrok (مجاني من ngrok.com)
- ✅ حساب لحظة (lahza.io)

### المتطلبات الاختيارية | Optional Requirements
- PowerShell 5.0+ (للسكريبت المتقدم)
- Chocolatey (لتثبيت ngrok تلقائياً)

## 📋 خطوات التشغيل التفصيلية | Detailed Steps

### الخطوة 1: إعداد البيئة
1. **تشغيل XAMPP**:
   - افتح XAMPP Control Panel
   - ابدأ Apache و MySQL

2. **تثبيت ngrok**:
   - حمل من: https://ngrok.com/download
   - أو: `choco install ngrok`

3. **إنشاء حساب ngrok**:
   - اذهب إلى: https://dashboard.ngrok.com/signup
   - احصل على Auth Token

### الخطوة 2: تشغيل النظام
```bash
# انقر نقرة مزدوجة
START_LAHZA_TESTING.bat
```

### الخطوة 3: تحديث لحظة
1. **نسخ webhook URL**:
   - من صفحة "Lahza Webhook Updater"
   - URL مثل: `https://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php`

2. **تحديث لوحة تحكم لحظة**:
   - اذهب إلى: https://dashboard.lahza.io
   - إعدادات Webhook
   - الصق URL الجديد

### الخطوة 4: اختبار النظام
1. **إنشاء فاتورة اختبار**:
   - WHMCS Admin → Billing → Create Invoice
   - Payment Method: Lahza

2. **معالجة دفع اختبار**:
   - بطاقة: `4111111111111111`
   - CVV: `004`
   - انتهاء: `03/30`

3. **التحقق من النتائج**:
   - حالة الفاتورة: "Paid"
   - webhook وصل في ngrok interface

## 🖥️ الواجهات المتاحة | Available Interfaces

### أثناء التشغيل | During Operation
- **ngrok Interface**: http://localhost:4040
- **Webhook Updater**: http://localhost/Whmcs/update_lahza_webhook.php
- **WHMCS Admin**: http://localhost/Whmcs/admin/
- **Test Suite**: http://localhost/Whmcs/final_test_suite.php

### لوحات التحكم الخارجية | External Dashboards
- **Lahza Dashboard**: https://dashboard.lahza.io
- **ngrok Dashboard**: https://dashboard.ngrok.com

## 🔍 مراقبة النظام | System Monitoring

### مراقبة webhook | Webhook Monitoring
1. **ngrok Interface** (http://localhost:4040):
   - عرض الطلبات الواردة
   - تفاصيل webhook requests

2. **WHMCS Gateway Logs**:
   - Admin → Logs → Gateway Log
   - ابحث عن "lahza"

### مراقبة الأخطاء | Error Monitoring
- **Module Logs**: Admin → Logs → Module Log
- **Activity Logs**: Admin → Logs → Activity Log

## ⚠️ استكشاف الأخطاء | Troubleshooting

### مشكلة: ngrok لا يعمل
```bash
# تحقق من التثبيت
ngrok version

# إعادة تعيين Auth Token
ngrok config add-authtoken YOUR_TOKEN
```

### مشكلة: webhook لا يصل
1. تحقق من ngrok interface
2. تأكد من URL في لوحة تحكم لحظة
3. استخدم HTTPS (ليس HTTP)

### مشكلة: الفاتورة لا تتحدث
1. فحص Gateway Logs
2. تشغيل Test Suite
3. إضافة الدفع يدوياً كحل مؤقت

## 📊 مؤشرات النجاح | Success Indicators

### ✅ النظام يعمل بشكل صحيح عندما:
- ngrok tunnel نشط ومتاح
- webhook URL محدث في لحظة
- webhook يصل ويظهر في ngrok interface
- حالة الفاتورة تتحدث إلى "Paid"
- سجل الدفع يظهر في WHMCS

### ❌ علامات وجود مشاكل:
- ngrok tunnel غير متاح
- webhook لا يظهر في ngrok interface
- أخطاء في Gateway Logs
- حالة الفاتورة تبقى "Unpaid"

## 🔄 إيقاف النظام | Stopping the System

### إيقاف آمن | Safe Shutdown
1. اضغط `Ctrl+C` في نافذة ngrok
2. أغلق نافذة `START_LAHZA_TESTING.bat`
3. أوقف XAMPP إذا لزم الأمر

### تنظيف النظام | System Cleanup
- ملفات ngrok مؤقتة تُحذف تلقائياً
- سجلات WHMCS تبقى للمراجعة

## 📞 الدعم | Support

### للمساعدة السريعة | Quick Help
1. راجع `WEBHOOK_TROUBLESHOOTING.md`
2. فحص Gateway Logs في WHMCS
3. استخدم Test Suite للتشخيص

### للمشاكل المتقدمة | Advanced Issues
1. راجع `WHMCS_LAHZA_ANALYSIS.md`
2. فحص ngrok logs
3. تحقق من إعدادات لحظة

---

## 🎉 تهانينا!

إذا وصلت إلى هنا، فقد تم إعداد نظام دفع لحظة بنجاح! 

النظام الآن جاهز للاختبار والاستخدام مع حل مشكلة webhook بالكامل.
