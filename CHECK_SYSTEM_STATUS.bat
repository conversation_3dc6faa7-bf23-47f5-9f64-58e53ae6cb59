@echo off
title System Status Check - Lahza Gateway
color 0B

echo.
echo ========================================
echo    SYSTEM STATUS CHECK
echo ========================================
echo.

REM Check XAMPP Apache
echo [1] Checking Apache (Port 80)...
netstat -an | find "80" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo     ✅ Apache is running on port 80
) else (
    echo     ❌ Apache is NOT running on port 80
    echo     → Start XAMPP Control Panel and start Apache
)

echo.

REM Check XAMPP MySQL
echo [2] Checking MySQL (Port 3306)...
netstat -an | find "3306" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    echo     ✅ MySQL is running on port 3306
) else (
    echo     ❌ MySQL is NOT running on port 3306
    echo     → Start XAMPP Control Panel and start MySQL
)

echo.

REM Check ngrok installation
echo [3] Checking ngrok installation...
where ngrok >nul 2>nul
if %errorlevel% equ 0 (
    echo     ✅ ngrok is installed and in PATH
    ngrok version 2>nul | find "version"
) else (
    echo     ❌ ngrok is NOT installed or not in PATH
    echo     → Download from: https://ngrok.com/download
    echo     → Extract to a folder in your PATH
)

echo.

REM Check ngrok tunnel
echo [4] Checking ngrok tunnel...
curl -s http://localhost:4040/api/tunnels >nul 2>nul
if %errorlevel% equ 0 (
    echo     ✅ ngrok tunnel is running
    echo     → Interface: http://localhost:4040
) else (
    echo     ❌ ngrok tunnel is NOT running
    echo     → Run: ngrok http 80
)

echo.

REM Check WHMCS files
echo [5] Checking WHMCS files...
if exist "modules\gateways\lahza.php" (
    echo     ✅ Lahza gateway file exists
) else (
    echo     ❌ Lahza gateway file missing
    echo     → Check: modules\gateways\lahza.php
)

if exist "modules\gateways\callback\lahza.php" (
    echo     ✅ Lahza callback file exists
) else (
    echo     ❌ Lahza callback file missing
    echo     → Check: modules\gateways\callback\lahza.php
)

echo.

REM Check automation files
echo [6] Checking automation files...
if exist "START_LAHZA_WITH_TOKEN.bat" (
    echo     ✅ Auto-start file ready
) else (
    echo     ❌ Auto-start file missing
    echo     → File: START_LAHZA_WITH_TOKEN.bat
)

if exist "update_lahza_webhook.php" (
    echo     ✅ Webhook updater ready
) else (
    echo     ❌ Webhook updater missing
    echo     → File: update_lahza_webhook.php
)

echo.

REM Summary
echo ========================================
echo    SUMMARY
echo ========================================
echo.

REM Count issues
set issues=0

netstat -an | find "80" | find "LISTENING" >nul
if %errorlevel% neq 0 set /a issues+=1

netstat -an | find "3306" | find "LISTENING" >nul
if %errorlevel% neq 0 set /a issues+=1

where ngrok >nul 2>nul
if %errorlevel% neq 0 set /a issues+=1

if %issues% equ 0 (
    echo ✅ ALL SYSTEMS READY!
    echo.
    echo You can now run:
    echo → START_LAHZA_WITH_TOKEN.bat
    echo.
    echo Next steps:
    echo 1. Double-click START_LAHZA_WITH_TOKEN.bat
    echo 2. Copy webhook URL from browser
    echo 3. Update Lahza dashboard
    echo 4. Test payment
) else (
    echo ❌ %issues% ISSUE(S) FOUND
    echo.
    echo Please fix the issues above before running:
    echo → START_LAHZA_WITH_TOKEN.bat
)

echo.
echo ========================================
echo    QUICK ACTIONS
echo ========================================
echo.
echo [1] Start XAMPP Control Panel
echo [2] Download ngrok
echo [3] Run auto-start script
echo [4] Open WHMCS admin
echo [5] Check this status again
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    if exist "C:\xampp\xampp-control.exe" (
        start "" "C:\xampp\xampp-control.exe"
    ) else (
        echo XAMPP not found at C:\xampp\
    )
)

if "%choice%"=="2" (
    start https://ngrok.com/download
)

if "%choice%"=="3" (
    if exist "START_LAHZA_WITH_TOKEN.bat" (
        start "" "START_LAHZA_WITH_TOKEN.bat"
    ) else (
        echo Auto-start file not found
    )
)

if "%choice%"=="4" (
    start http://localhost/Whmcs/admin/
)

if "%choice%"=="5" (
    cls
    goto :eof
)

echo.
pause
