<?php
/**
 * Automatic Lahza Webhook URL Updater
 * This script helps update the webhook URL in Lahza dashboard
 */

require_once __DIR__ . '/init.php';

echo "<h1>🔧 Lahza Webhook URL Updater</h1>";

// Get current ngrok URL
function getNgrokUrl() {
    try {
        $response = @file_get_contents('http://localhost:4040/api/tunnels');
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['tunnels'])) {
                foreach ($data['tunnels'] as $tunnel) {
                    if ($tunnel['proto'] === 'https') {
                        return $tunnel['public_url'];
                    }
                }
            }
        }
    } catch (Exception $e) {
        // Ignore errors
    }
    return null;
}

$ngrokUrl = getNgrokUrl();

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";

if ($ngrokUrl) {
    $webhookUrl = $ngrokUrl . '/Whmcs/modules/gateways/callback/lahza.php';
    
    echo "<h2>✅ ngrok Tunnel Detected</h2>";
    echo "<p><strong>Public URL:</strong> <code>" . htmlspecialchars($ngrokUrl) . "</code></p>";
    echo "<p><strong>Webhook URL:</strong></p>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
    echo "<code style='font-size: 14px; word-break: break-all;'>" . htmlspecialchars($webhookUrl) . "</code>";
    echo "</div>";
    
    echo "<button onclick='copyToClipboard(\"" . htmlspecialchars($webhookUrl) . "\")' style='padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>📋 Copy Webhook URL</button>";
    
} else {
    echo "<h2>❌ ngrok Tunnel Not Found</h2>";
    echo "<p>Please make sure ngrok is running on port 80.</p>";
    echo "<p>Run this command in terminal:</p>";
    echo "<code style='background: #333; color: #fff; padding: 10px; display: block; border-radius: 4px;'>ngrok http 80</code>";
}

echo "</div>";

// Instructions for updating Lahza dashboard
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>📋 How to Update Lahza Dashboard</h2>";
echo "<ol>";
echo "<li><strong>Login to Lahza Dashboard:</strong>";
echo "    <ul>";
echo "        <li>Go to: <a href='https://dashboard.lahza.io' target='_blank'>https://dashboard.lahza.io</a></li>";
echo "        <li>Login with your credentials</li>";
echo "    </ul>";
echo "</li>";
echo "<li><strong>Navigate to Webhook Settings:</strong>";
echo "    <ul>";
echo "        <li>Look for 'Webhooks' or 'API Settings' in the menu</li>";
echo "        <li>Find the webhook URL configuration</li>";
echo "    </ul>";
echo "</li>";
echo "<li><strong>Update Webhook URL:</strong>";
echo "    <ul>";
echo "        <li>Replace the existing URL with the one above</li>";
echo "        <li>Make sure to use the HTTPS version</li>";
echo "        <li>Save the changes</li>";
echo "    </ul>";
echo "</li>";
echo "<li><strong>Enable Webhook Events:</strong>";
echo "    <ul>";
echo "        <li>Enable: <code>charge.success</code></li>";
echo "        <li>Enable: <code>charge.failed</code></li>";
echo "        <li>Enable: <code>refund.processed</code></li>";
echo "    </ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

// Test webhook functionality
echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>🧪 Test Webhook Functionality</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_webhook']) && $ngrokUrl) {
    
    echo "<h3>Testing Webhook...</h3>";
    
    $testData = [
        'event' => 'charge.success',
        'data' => [
            'id' => 'test_' . time(),
            'reference' => 'INV-999-test-' . date('YmdHis'),
            'status' => 'success',
            'amount' => 1000, // $10.00
            'fees' => 30,     // $0.30
            'metadata' => [
                'invoiceid' => '999',
                'clientid' => '1'
            ]
        ]
    ];
    
    $webhookUrl = $ngrokUrl . '/Whmcs/modules/gateways/callback/lahza.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $webhookUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'User-Agent: Lahza-Test-Webhook/1.0'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<h4>Test Results:</h4>";
    echo "<p><strong>HTTP Code:</strong> " . $httpCode . "</p>";
    
    if ($error) {
        echo "<p><strong>❌ Error:</strong> " . htmlspecialchars($error) . "</p>";
    } else {
        echo "<p><strong>✅ Response:</strong></p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px;'>" . htmlspecialchars($response) . "</pre>";
        
        if ($httpCode == 200) {
            echo "<p style='color: green;'><strong>🎉 Webhook test successful!</strong></p>";
        } else {
            echo "<p style='color: red;'><strong>❌ Webhook test failed</strong></p>";
        }
    }
    
} else {
    if ($ngrokUrl) {
        echo "<p>Test if your webhook URL is working correctly:</p>";
        echo "<form method='post'>";
        echo "<button type='submit' name='test_webhook' style='padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;'>🚀 Test Webhook</button>";
        echo "</form>";
    } else {
        echo "<p>⚠️ Cannot test webhook without ngrok tunnel</p>";
    }
}

echo "</div>";

// Quick links
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>🔗 Quick Links</h2>";
echo "<ul>";
echo "<li><a href='http://localhost:4040' target='_blank'>ngrok Web Interface</a></li>";
echo "<li><a href='/Whmcs/final_test_suite.php' target='_blank'>Final Test Suite</a></li>";
echo "<li><a href='/Whmcs/admin/systemgatewaylog.php' target='_blank'>WHMCS Gateway Logs</a></li>";
echo "<li><a href='https://dashboard.lahza.io' target='_blank'>Lahza Dashboard</a></li>";
echo "</ul>";
echo "</div>";

// Auto-refresh functionality
echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>🔄 Auto-Refresh</h3>";
echo "<p>This page will auto-refresh every 30 seconds to check for ngrok URL changes.</p>";
echo "<button onclick='toggleAutoRefresh()' id='refreshBtn' style='padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;'>⏸️ Pause Auto-Refresh</button>";
echo "</div>";

// JavaScript for functionality
echo "<script>";
echo "let autoRefreshEnabled = true;";
echo "let refreshInterval;";

echo "function copyToClipboard(text) {";
echo "    navigator.clipboard.writeText(text).then(function() {";
echo "        alert('Webhook URL copied to clipboard!');";
echo "    }, function(err) {";
echo "        console.error('Could not copy text: ', err);";
echo "        prompt('Copy this URL:', text);";
echo "    });";
echo "}";

echo "function toggleAutoRefresh() {";
echo "    const btn = document.getElementById('refreshBtn');";
echo "    if (autoRefreshEnabled) {";
echo "        clearInterval(refreshInterval);";
echo "        btn.textContent = '▶️ Resume Auto-Refresh';";
echo "        btn.style.background = '#28a745';";
echo "        autoRefreshEnabled = false;";
echo "    } else {";
echo "        startAutoRefresh();";
echo "        btn.textContent = '⏸️ Pause Auto-Refresh';";
echo "        btn.style.background = '#6c757d';";
echo "        autoRefreshEnabled = true;";
echo "    }";
echo "}";

echo "function startAutoRefresh() {";
echo "    refreshInterval = setInterval(function() {";
echo "        if (autoRefreshEnabled) {";
echo "            window.location.reload();";
echo "        }";
echo "    }, 30000);";
echo "}";

echo "// Start auto-refresh on page load";
echo "startAutoRefresh();";

echo "// Show countdown";
echo "let countdown = 30;";
echo "setInterval(function() {";
echo "    if (autoRefreshEnabled) {";
echo "        countdown--;";
echo "        if (countdown <= 0) countdown = 30;";
echo "        document.title = 'Lahza Webhook Updater (' + countdown + 's)';";
echo "    }";
echo "}, 1000);";

echo "</script>";

// CSS for better styling
echo "<style>";
echo "body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #333; }";
echo "code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; }";
echo "pre { background: #f8f9fa; padding: 15px; border-radius: 8px; overflow-x: auto; }";
echo "button:hover { opacity: 0.9; }";
echo "a { color: #007cba; text-decoration: none; }";
echo "a:hover { text-decoration: underline; }";
echo "</style>";
?>
