# Changelog

All notable changes to the Lahza.io Payment Gateway for WHMCS will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [3.0.0] - 2025-01-15

### Added
- **WHMCS 8.x Compatibility**: Full compatibility with WHMCS 8.x
- **Enhanced Security**: Webhook signature verification with HMAC-SHA256
- **IP Whitelisting**: Restrict webhook sources to Lahza.io IPs
- **RTL Language Support**: Arabic, Hebrew, Farsi language support
- **Dark Mode Support**: Automatic theme detection and dark mode styling
- **Mobile Optimization**: Responsive design for all devices
- **Card Type Detection**: Display card brand and bank information
- **Comprehensive Logging**: Detailed transaction and error logging
- **Debug Mode**: Enhanced debugging capabilities with detailed logs
- **Custom CSS Support**: Configurable styling options
- **Metadata Support**: Rich transaction metadata for better tracking
- **Error Recovery**: Automatic retry mechanisms for failed requests
- **Template Integration**: Optimized integration with WIDDX template
- **Order Form Support**: Compatible with all WHMCS order forms

### Enhanced
- **API Integration**: Updated to Lahza.io API v2024
- **Error Handling**: User-friendly error messages with detailed logging
- **Payment Flow**: Improved popup and redirect payment methods
- **Transaction Validation**: Enhanced validation and verification
- **Performance**: Optimized API calls and response handling
- **Documentation**: Comprehensive documentation and troubleshooting guides

### Security
- **SSL Enforcement**: Mandatory SSL for all transactions
- **Input Validation**: Enhanced validation for all user inputs
- **XSS Protection**: Protection against cross-site scripting
- **CSRF Protection**: Cross-site request forgery protection
- **Data Sanitization**: Proper sanitization of all data

### Fixed
- **Webhook Processing**: Improved webhook handling and error recovery
- **Currency Conversion**: Accurate currency conversion to cents/agora/qirsh
- **Transaction References**: Unique transaction reference generation
- **Memory Leaks**: Fixed potential memory leaks in API calls
- **Race Conditions**: Eliminated race conditions in payment processing

## [2.1.0] - 2024-12-01

### Added
- **Multi-Currency Support**: Added support for ILS and JOD currencies
- **Enhanced Logging**: More detailed transaction logging
- **Webhook Improvements**: Better webhook handling and validation

### Fixed
- **API Timeout Issues**: Improved timeout handling for API calls
- **Error Message Display**: Better error message formatting
- **Mobile Responsiveness**: Fixed mobile display issues

## [2.0.0] - 2024-10-15

### Added
- **Webhook Support**: Real-time payment notifications
- **Card Type Detection**: Display card brand and issuing bank
- **Refund Support**: Process refunds through WHMCS admin
- **Test Mode**: Comprehensive testing capabilities
- **Configuration Options**: Extended configuration options

### Changed
- **API Integration**: Updated to use latest Lahza.io API
- **Payment Flow**: Improved payment user experience
- **Error Handling**: Enhanced error handling and reporting

### Security
- **Webhook Verification**: Added webhook signature verification
- **Input Validation**: Enhanced input validation and sanitization

## [1.2.0] - 2024-08-20

### Added
- **Popup Payment Method**: Added popup payment option
- **Custom Styling**: Basic custom CSS support
- **Transaction Logging**: Basic transaction logging

### Fixed
- **Redirect Issues**: Fixed payment redirect problems
- **Currency Handling**: Improved currency conversion
- **Error Messages**: Better error message display

## [1.1.0] - 2024-06-10

### Added
- **Redirect Payment Method**: Payment via redirect to Lahza.io
- **Basic Configuration**: Essential configuration options
- **USD Support**: Support for USD currency

### Fixed
- **API Connection**: Improved API connection stability
- **Payment Processing**: Fixed payment processing issues

## [1.0.0] - 2024-04-01

### Added
- **Initial Release**: Basic Lahza.io payment gateway integration
- **Payment Processing**: Core payment processing functionality
- **API Integration**: Basic API integration with Lahza.io
- **WHMCS Integration**: Integration with WHMCS payment system

### Features
- Basic payment processing
- USD currency support
- Simple configuration
- Basic error handling

---

## Migration Guide

### From 2.x to 3.0.0

#### Breaking Changes
- **Minimum WHMCS Version**: Now requires WHMCS 8.0.0 or higher
- **PHP Version**: Now requires PHP 7.4.0 or higher
- **SSL Requirement**: SSL is now mandatory for all environments

#### Configuration Updates
1. **Enable Logging**: Recommended to enable logging for better debugging
2. **Webhook Configuration**: Update webhook URL in Lahza.io dashboard
3. **IP Whitelist**: Configure IP whitelist for enhanced security

#### Template Updates
- **WIDDX Template**: Enhanced integration with WIDDX template
- **Custom CSS**: Update custom CSS if using custom styling
- **RTL Support**: Automatic RTL support for Arabic/Hebrew/Farsi

### From 1.x to 2.0.0

#### Breaking Changes
- **API Changes**: Updated API integration requires new configuration
- **Webhook Support**: New webhook functionality requires setup

#### Required Actions
1. **Update Configuration**: Review and update gateway configuration
2. **Setup Webhooks**: Configure webhooks in Lahza.io dashboard
3. **Test Integration**: Thoroughly test payment flow after upgrade

---

## Support

For support with any version:
- **Documentation**: Check README.md and TROUBLESHOOTING.md
- **Lahza.io Support**: <EMAIL>
- **WHMCS Support**: https://www.whmcs.com/support/

## Contributing

We welcome contributions! Please see our contributing guidelines for details on how to submit improvements and bug fixes.

---

**Note**: Always backup your WHMCS installation before upgrading the payment gateway module.