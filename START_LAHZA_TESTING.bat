@echo off
title Lahza Payment Gateway - Quick Start
color 0A

echo.
echo  ██╗      █████╗ ██╗  ██╗███████╗ █████╗ 
echo  ██║     ██╔══██╗██║  ██║╚══███╔╝██╔══██╗
echo  ██║     ███████║███████║  ███╔╝ ███████║
echo  ██║     ██╔══██║██╔══██║ ███╔╝  ██╔══██║
echo  ███████╗██║  ██║██║  ██║███████╗██║  ██║
echo  ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
echo.
echo  Payment Gateway Testing Environment
echo  ====================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Not running as administrator
    echo Some features may not work properly
    echo.
)

REM Step 1: Check XAMPP
echo [STEP 1] Checking XAMPP Status...
echo.

REM Check if Apache is running
netstat -an | find "80" | find "LISTENING" >nul
if %errorlevel% neq 0 (
    echo [INFO] Starting XAMPP...
    
    REM Try to start XAMPP services
    if exist "C:\xampp\xampp_start.exe" (
        start "" "C:\xampp\xampp_start.exe"
        timeout /t 5 /nobreak >nul
    )
    
    REM Open XAMPP Control Panel
    if exist "C:\xampp\xampp-control.exe" (
        echo [INFO] Opening XAMPP Control Panel...
        start "" "C:\xampp\xampp-control.exe"
        echo.
        echo Please start Apache and MySQL services
        echo Then press any key to continue...
        pause >nul
    ) else (
        echo [ERROR] XAMPP not found at C:\xampp\
        echo Please install XAMPP or start your web server manually
        echo.
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] Apache is running on port 80
)

REM Check MySQL
netstat -an | find "3306" | find "LISTENING" >nul
if %errorlevel% neq 0 (
    echo [WARNING] MySQL may not be running on port 3306
) else (
    echo [SUCCESS] MySQL is running on port 3306
)

echo.

REM Step 2: Check ngrok
echo [STEP 2] Checking ngrok Installation...
echo.

where ngrok >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] ngrok is not installed
    echo.
    echo Please install ngrok:
    echo 1. Download from: https://ngrok.com/download
    echo 2. Extract to a folder in your PATH
    echo 3. Or install via Chocolatey: choco install ngrok
    echo.
    echo Opening ngrok download page...
    start https://ngrok.com/download
    echo.
    pause
    exit /b 1
) else (
    echo [SUCCESS] ngrok is installed
)

REM Check ngrok auth token
ngrok config check >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] ngrok auth token not configured
    echo.
    echo Please get your auth token:
    echo 1. Go to: https://dashboard.ngrok.com/get-started/your-authtoken
    echo 2. Sign up for free if needed
    echo 3. Copy your auth token
    echo.
    start https://dashboard.ngrok.com/get-started/your-authtoken
    echo.
    set /p authtoken="Enter your ngrok auth token: "
    
    if not "!authtoken!"=="" (
        ngrok config add-authtoken !authtoken!
        if !errorlevel! equ 0 (
            echo [SUCCESS] Auth token added
        ) else (
            echo [ERROR] Failed to add auth token
            pause
            exit /b 1
        )
    ) else (
        echo [ERROR] Auth token is required
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] ngrok auth token is configured
)

echo.

REM Step 3: Start ngrok tunnel
echo [STEP 3] Starting ngrok Tunnel...
echo.

echo [INFO] Starting ngrok tunnel for port 80...
start "ngrok-tunnel" ngrok http 80

echo [INFO] Waiting for ngrok to initialize...
timeout /t 8 /nobreak >nul

REM Step 4: Open interfaces
echo [STEP 4] Opening Web Interfaces...
echo.

echo [INFO] Opening ngrok web interface...
start http://localhost:4040

timeout /t 2 /nobreak >nul

echo [INFO] Opening Lahza webhook updater...
start http://localhost/Whmcs/update_lahza_webhook.php

timeout /t 2 /nobreak >nul

echo [INFO] Opening WHMCS admin...
start http://localhost/Whmcs/admin/

timeout /t 2 /nobreak >nul

echo [INFO] Opening final test suite...
start http://localhost/Whmcs/final_test_suite.php

echo.

REM Step 5: Instructions
echo ========================================
echo    SETUP COMPLETE - NEXT STEPS
echo ========================================
echo.
echo 1. COPY WEBHOOK URL:
echo    - Check the "Lahza Webhook Updater" tab
echo    - Copy the webhook URL shown
echo.
echo 2. UPDATE LAHZA DASHBOARD:
echo    - Go to: https://dashboard.lahza.io
echo    - Navigate to Webhook settings
echo    - Paste the webhook URL
echo    - Enable events: charge.success, charge.failed
echo.
echo 3. TEST THE GATEWAY:
echo    - Create a new invoice in WHMCS
echo    - Set payment method to "Lahza"
echo    - Use test card: ****************
echo    - CVV: 004, Expiry: 03/30
echo.
echo 4. MONITOR ACTIVITY:
echo    - ngrok interface: http://localhost:4040
echo    - WHMCS Gateway Logs: Admin → Logs → Gateway Log
echo.
echo ========================================
echo    TUNNEL IS RUNNING
echo ========================================
echo.
echo Keep this window open while testing!
echo.
echo Available interfaces:
echo - ngrok Web Interface: http://localhost:4040
echo - Webhook Updater: http://localhost/Whmcs/update_lahza_webhook.php
echo - WHMCS Admin: http://localhost/Whmcs/admin/
echo - Test Suite: http://localhost/Whmcs/final_test_suite.php
echo.

REM Keep the script running
echo Press any key to open Lahza dashboard...
pause >nul
start https://dashboard.lahza.io

echo.
echo [INFO] All systems ready for testing!
echo [INFO] Press Ctrl+C to stop when done
echo.

:loop
timeout /t 60 /nobreak >nul
echo [INFO] System running... (Press Ctrl+C to stop)
goto loop
