@echo off
title Fix ngrok Tunnel - Lahza Gateway
color 0C

echo.
echo ========================================
echo    FIXING NGROK TUNNEL
echo ========================================
echo.

REM Your pre-configured token
set NGROK_TOKEN=*************************************************

echo [INFO] Detected issue: ngrok tunnel not running
echo [INFO] Your token: %NGROK_TOKEN%
echo.

REM Step 1: Check if ngrok is installed
echo [STEP 1] Checking ngrok installation...
where ngrok >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] ngrok is not installed or not in PATH
    echo.
    echo Please install ngrok:
    echo 1. Download from: https://ngrok.com/download
    echo 2. Extract to C:\ngrok\ or add to PATH
    echo.
    start https://ngrok.com/download
    pause
    exit /b 1
) else (
    echo [SUCCESS] ngrok found
    ngrok version
)

echo.

REM Step 2: Kill any existing ngrok processes
echo [STEP 2] Stopping any existing ngrok processes...
taskkill /f /im ngrok.exe >nul 2>nul
if %errorlevel% equ 0 (
    echo [INFO] Stopped existing ngrok processes
) else (
    echo [INFO] No existing ngrok processes found
)

timeout /t 2 /nobreak >nul

REM Step 3: Configure auth token
echo [STEP 3] Configuring auth token...
ngrok config add-authtoken %NGROK_TOKEN%
if %errorlevel% equ 0 (
    echo [SUCCESS] Auth token configured
) else (
    echo [ERROR] Failed to configure auth token
    pause
    exit /b 1
)

echo.

REM Step 4: Check if port 80 is available
echo [STEP 4] Checking if Apache is running on port 80...
netstat -an | find "80" | find "LISTENING" >nul
if %errorlevel% neq 0 (
    echo [WARNING] Port 80 is not listening
    echo [INFO] Starting XAMPP...
    
    if exist "C:\xampp\xampp-control.exe" (
        start "" "C:\xampp\xampp-control.exe"
        echo Please start Apache in XAMPP Control Panel
        echo Then press any key to continue...
        pause >nul
    ) else (
        echo [ERROR] XAMPP not found. Please start your web server on port 80
        pause
        exit /b 1
    )
) else (
    echo [SUCCESS] Apache is running on port 80
)

echo.

REM Step 5: Start ngrok tunnel
echo [STEP 5] Starting ngrok tunnel...
echo.
echo [INFO] Starting ngrok http 80...
echo [INFO] This will create a public URL for localhost:80
echo.

start "ngrok-tunnel" ngrok http 80

echo [INFO] Waiting for ngrok to initialize...
timeout /t 8 /nobreak >nul

REM Step 6: Test ngrok API
echo [STEP 6] Testing ngrok API...
curl -s http://localhost:4040/api/tunnels >nul 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] ngrok API is responding
    echo [INFO] Opening ngrok interface...
    start http://localhost:4040
) else (
    echo [WARNING] ngrok API not responding yet
    echo [INFO] Please wait a moment and check manually
    echo [INFO] Opening ngrok interface...
    start http://localhost:4040
)

echo.

REM Step 7: Instructions
echo ========================================
echo    NGROK TUNNEL STARTED
echo ========================================
echo.
echo 1. CHECK NGROK INTERFACE:
echo    - URL: http://localhost:4040
echo    - Look for HTTPS tunnel URL
echo    - Should be like: https://abc123.ngrok.io
echo.
echo 2. UPDATE TEST SUITE:
echo    - Refresh: http://localhost/Whmcs/final_test_suite.php
echo    - Should now show detected ngrok URL
echo.
echo 3. UPDATE WEBHOOK UPDATER:
echo    - Open: http://localhost/Whmcs/update_lahza_webhook.php
echo    - Copy the webhook URL
echo    - Update Lahza dashboard
echo.
echo 4. KEEP THIS WINDOW OPEN:
echo    - ngrok tunnel runs in background
echo    - Close this to stop tunnel
echo.

echo ========================================
echo    QUICK LINKS
echo ========================================
echo.
echo [1] Open ngrok interface
echo [2] Open test suite
echo [3] Open webhook updater
echo [4] Open Lahza dashboard
echo [5] Keep tunnel running
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" start http://localhost:4040
if "%choice%"=="2" start http://localhost/Whmcs/final_test_suite.php
if "%choice%"=="3" start http://localhost/Whmcs/update_lahza_webhook.php
if "%choice%"=="4" start https://dashboard.lahza.io
if "%choice%"=="5" goto keep_running

:keep_running
echo.
echo [INFO] Tunnel is running in background
echo [INFO] Press Ctrl+C to stop when done
echo.

:monitor_loop
timeout /t 30 /nobreak >nul
echo [INFO] Tunnel active - monitoring... (Press Ctrl+C to stop)
goto monitor_loop
