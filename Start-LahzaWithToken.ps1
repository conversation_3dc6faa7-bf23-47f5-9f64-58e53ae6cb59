# Lahza Payment Gateway - Auto Setup with Pre-configured Token
# Your ngrok auth token: *************************************************

param(
    [int]$Port = 80,
    [switch]$SkipChecks = $false
)

$AuthToken = "*************************************************"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Lahza Gateway - Ready to Launch!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔑 Using your pre-configured ngrok token" -ForegroundColor Green
Write-Host ""

# Function to check if a command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Function to check if port is listening
function Test-Port {
    param($Port)
    try {
        $listener = Get-NetTCPConnection -LocalPort $Port -State Listen -ErrorAction SilentlyContinue
        return $listener -ne $null
    } catch {
        return $false
    }
}

# Function to get ngrok URL
function Get-NgrokUrl {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:4040/api/tunnels" -Method Get -ErrorAction Stop
        $httpsUrl = $response.tunnels | Where-Object { $_.proto -eq "https" } | Select-Object -First 1
        if ($httpsUrl) {
            return $httpsUrl.public_url
        }
        return $null
    } catch {
        return $null
    }
}

# Step 1: Check prerequisites
if (-not $SkipChecks) {
    Write-Host "[STEP 1] Checking system requirements..." -ForegroundColor Yellow
    
    # Check ngrok
    if (-not (Test-Command "ngrok")) {
        Write-Host "[ERROR] ngrok is not installed" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please install ngrok:" -ForegroundColor Yellow
        Write-Host "1. Download from: https://ngrok.com/download" -ForegroundColor White
        Write-Host "2. Extract to a folder in your PATH" -ForegroundColor White
        Write-Host ""
        Start-Process "https://ngrok.com/download"
        Read-Host "Press Enter after installing ngrok"
        
        if (-not (Test-Command "ngrok")) {
            Write-Host "[ERROR] ngrok still not found. Please check installation." -ForegroundColor Red
            exit 1
        }
    }
    Write-Host "[SUCCESS] ngrok found" -ForegroundColor Green
    
    # Check XAMPP/Apache
    if (-not (Test-Port $Port)) {
        Write-Host "[WARNING] Port $Port is not listening" -ForegroundColor Yellow
        Write-Host "Starting XAMPP Control Panel..." -ForegroundColor Yellow
        
        $xamppPath = "C:\xampp\xampp-control.exe"
        if (Test-Path $xamppPath) {
            Start-Process $xamppPath
            Write-Host ""
            Write-Host "Please start Apache and MySQL in XAMPP Control Panel" -ForegroundColor Cyan
            Read-Host "Press Enter when Apache is running"
        } else {
            Write-Host "[WARNING] XAMPP not found at $xamppPath" -ForegroundColor Yellow
            Write-Host "Please start your web server manually on port $Port" -ForegroundColor Yellow
            Read-Host "Press Enter when your web server is running"
        }
    }
    
    if (Test-Port $Port) {
        Write-Host "[SUCCESS] Web server is running on port $Port" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Port $Port is still not available" -ForegroundColor Red
        Write-Host "Please ensure your web server is running" -ForegroundColor Yellow
        exit 1
    }
}

# Step 2: Configure ngrok
Write-Host ""
Write-Host "[STEP 2] Configuring ngrok with your token..." -ForegroundColor Yellow

try {
    $result = & ngrok config add-authtoken $AuthToken 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[SUCCESS] Your auth token configured successfully" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Failed to configure auth token: $result" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "[ERROR] Failed to configure auth token: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Start ngrok tunnel
Write-Host ""
Write-Host "[STEP 3] Starting ngrok tunnel..." -ForegroundColor Yellow

# Start ngrok in background
$ngrokJob = Start-Job -ScriptBlock {
    param($Port)
    & ngrok http $Port
} -ArgumentList $Port

# Wait for ngrok to start
Write-Host "[INFO] Waiting for ngrok to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 6

# Get ngrok URL
$maxAttempts = 12
$attempt = 0
$ngrokUrl = $null

do {
    $attempt++
    Write-Host "[INFO] Attempt $attempt to get ngrok URL..." -ForegroundColor Yellow
    $ngrokUrl = Get-NgrokUrl
    if ($ngrokUrl) {
        break
    }
    Start-Sleep -Seconds 2
} while ($attempt -lt $maxAttempts)

if ($ngrokUrl) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "   🚀 NGROK TUNNEL ACTIVE!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Public URL: " -NoNewline -ForegroundColor White
    Write-Host $ngrokUrl -ForegroundColor Cyan
    Write-Host "Local URL:  http://localhost:$Port" -ForegroundColor White
    Write-Host ""
    
    # Generate webhook URL
    $webhookUrl = "$ngrokUrl/Whmcs/modules/gateways/callback/lahza.php"
    Write-Host "🔗 Webhook URL for Lahza Dashboard:" -ForegroundColor Yellow
    Write-Host $webhookUrl -ForegroundColor Cyan
    
    # Copy to clipboard
    try {
        $webhookUrl | Set-Clipboard
        Write-Host ""
        Write-Host "✅ Webhook URL copied to clipboard!" -ForegroundColor Green
    } catch {
        Write-Host ""
        Write-Host "ℹ️  Please copy the webhook URL manually" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "[ERROR] Could not get ngrok URL after $maxAttempts attempts" -ForegroundColor Red
    Write-Host "Please check http://localhost:4040 manually" -ForegroundColor Yellow
}

# Step 4: Open all interfaces
Write-Host ""
Write-Host "[STEP 4] Opening management interfaces..." -ForegroundColor Yellow

# Open ngrok web interface
Write-Host "Opening ngrok interface..." -ForegroundColor White
Start-Process "http://localhost:4040"
Start-Sleep -Seconds 2

# Open webhook updater
Write-Host "Opening webhook updater..." -ForegroundColor White
$webhookUpdaterUrl = if ($ngrokUrl) { "$ngrokUrl/Whmcs/update_lahza_webhook.php" } else { "http://localhost/Whmcs/update_lahza_webhook.php" }
Start-Process $webhookUpdaterUrl
Start-Sleep -Seconds 2

# Open WHMCS admin
Write-Host "Opening WHMCS admin..." -ForegroundColor White
$adminUrl = if ($ngrokUrl) { "$ngrokUrl/Whmcs/admin/" } else { "http://localhost/Whmcs/admin/" }
Start-Process $adminUrl
Start-Sleep -Seconds 2

# Open test suite
Write-Host "Opening test suite..." -ForegroundColor White
$testSuiteUrl = if ($ngrokUrl) { "$ngrokUrl/Whmcs/final_test_suite.php" } else { "http://localhost/Whmcs/final_test_suite.php" }
Start-Process $testSuiteUrl
Start-Sleep -Seconds 2

# Step 5: Instructions
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   📋 NEXT STEPS" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 🔗 Update Lahza Dashboard:" -ForegroundColor Yellow
Write-Host "   • Go to: https://dashboard.lahza.io" -ForegroundColor White
Write-Host "   • Login to your account" -ForegroundColor White
Write-Host "   • Find Webhook/API settings" -ForegroundColor White
Write-Host "   • Set webhook URL to:" -ForegroundColor White
Write-Host "     $webhookUrl" -ForegroundColor Cyan
Write-Host "   • Enable events: charge.success, charge.failed" -ForegroundColor White
Write-Host ""
Write-Host "2. 🧪 Test Payment:" -ForegroundColor Yellow
Write-Host "   • Create new invoice in WHMCS" -ForegroundColor White
Write-Host "   • Set payment method to 'Lahza'" -ForegroundColor White
Write-Host "   • Use test card: ****************" -ForegroundColor White
Write-Host "   • CVV: 004, Expiry: 03/30" -ForegroundColor White
Write-Host ""
Write-Host "3. 📊 Monitor Results:" -ForegroundColor Yellow
Write-Host "   • ngrok interface: http://localhost:4040" -ForegroundColor White
Write-Host "   • WHMCS Gateway Logs: Admin → Logs → Gateway Log" -ForegroundColor White
Write-Host "   • Invoice should change to 'Paid' status" -ForegroundColor White
Write-Host ""

# Open Lahza dashboard
Write-Host "Opening Lahza dashboard..." -ForegroundColor White
Start-Process "https://dashboard.lahza.io"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   ✅ ALL SYSTEMS READY!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "🔄 Tunnel is running and monitoring..." -ForegroundColor Yellow
Write-Host "🛑 Press Ctrl+C to stop when done" -ForegroundColor Yellow
Write-Host ""

# Monitor the tunnel
try {
    $lastUrl = $ngrokUrl
    while ($true) {
        # Check if ngrok is still running
        if ($ngrokJob.State -ne "Running") {
            Write-Host "[ERROR] ngrok tunnel stopped unexpectedly" -ForegroundColor Red
            break
        }
        
        # Check tunnel status every 30 seconds
        Start-Sleep -Seconds 30
        
        $currentUrl = Get-NgrokUrl
        if ($currentUrl -ne $lastUrl) {
            if ($currentUrl) {
                Write-Host "[INFO] Tunnel URL: $currentUrl" -ForegroundColor Cyan
                $lastUrl = $currentUrl
            } else {
                Write-Host "[WARNING] Tunnel URL not accessible" -ForegroundColor Yellow
            }
        } else {
            Write-Host "[INFO] Tunnel active: $currentUrl" -ForegroundColor Green
        }
    }
} catch {
    Write-Host ""
    Write-Host "[INFO] Tunnel stopped by user" -ForegroundColor Yellow
} finally {
    # Cleanup
    if ($ngrokJob) {
        Stop-Job $ngrokJob -Force
        Remove-Job $ngrokJob -Force
    }
    Write-Host "[INFO] Cleanup completed" -ForegroundColor Green
}
