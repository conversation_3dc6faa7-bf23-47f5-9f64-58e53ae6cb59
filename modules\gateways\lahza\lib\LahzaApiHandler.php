<?php
/**
 * Developed by WIDDX (https://widdx.com)
 * © 2025 All Rights Reserved
 * 
 * Lahza API Handler for WHMCS Payment Gateway
 */

class LahzaApiHandler
{
    /**
     * @var string Lahza API base URL
     */
    private $apiBaseUrl;

    /**
     * @var string Public key for API authentication
     */
    private $publicKey;

    /**
     * @var string Secret key for API authentication
     */
    private $secretKey;

    /**
     * @var bool Test mode flag
     */
    private $testMode;

    /**
     * @var array Default cURL options
     */
    private $curlOptions;

    /**
     * Constructor
     *
     * @param string $publicKey Lahza public key
     * @param string $secretKey Lahza secret key
     * @param bool $testMode Whether to use test mode
     */
    public function __construct($publicKey, $secretKey, $testMode = false)
    {
        $this->publicKey = $publicKey;
        $this->secretKey = $secretKey;
        $this->testMode = $testMode;
        
        // Set API base URL - use correct Lahza API endpoint
        // Note: <PERSON><PERSON>za uses the same API endpoint for both test and production
        // The test/live mode is determined by the API keys used
        $this->apiBaseUrl = 'https://api.lahza.io';
        
        // Set default cURL options with enhanced connectivity settings
        $this->curlOptions = array(
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 15,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_USERAGENT => 'WHMCS-Lahza-Gateway/1.1.0',
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_DNS_CACHE_TIMEOUT => 120,
            CURLOPT_FRESH_CONNECT => false,
            CURLOPT_FORBID_REUSE => false,
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $this->secretKey,
                'Content-Type: application/json',
                'Accept: application/json',
                'Cache-Control: no-cache',
                'Connection: keep-alive'
            )
        );
    }

    /**
     * Initialize a transaction
     *
     * @param array $transactionData Transaction data
     * @return array|false API response or false on failure
     */
    public function initializeTransaction($transactionData)
    {
        $endpoint = '/transaction/initialize';
        
        // Validate required fields for Lahza API
        $requiredFields = array('email', 'mobile', 'amount');
        foreach ($requiredFields as $field) {
            if (!isset($transactionData[$field]) || empty($transactionData[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }

        // Sanitize and validate data
        $sanitizedData = $this->sanitizeTransactionData($transactionData);
        
        return $this->makeApiRequest('POST', $endpoint, $sanitizedData);
    }

    /**
     * Verify a transaction
     *
     * @param string $reference Transaction reference
     * @return array|false API response or false on failure
     */
    public function verifyTransaction($reference)
    {
        if (empty($reference)) {
            throw new Exception("Transaction reference is required");
        }

        $endpoint = '/transaction/verify/' . urlencode($reference);
        
        return $this->makeApiRequest('GET', $endpoint);
    }

    /**
     * Process a refund
     *
     * @param array $refundData Refund data
     * @return array|false API response or false on failure
     */
    public function processRefund($refundData)
    {
        $endpoint = '/refund';
        
        // Validate required fields
        $requiredFields = array('transaction', 'amount');
        foreach ($requiredFields as $field) {
            if (!isset($refundData[$field]) || empty($refundData[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }

        // Sanitize data
        $sanitizedData = $this->sanitizeRefundData($refundData);
        
        return $this->makeApiRequest('POST', $endpoint, $sanitizedData);
    }

    /**
     * Cancel a subscription
     *
     * @param string $subscriptionId Subscription ID
     * @return array|false API response or false on failure
     */
    public function cancelSubscription($subscriptionId)
    {
        if (empty($subscriptionId)) {
            throw new Exception("Subscription ID is required");
        }

        $endpoint = '/subscription/cancel/' . urlencode($subscriptionId);
        
        return $this->makeApiRequest('POST', $endpoint);
    }

    /**
     * Get transaction details
     *
     * @param string $transactionId Transaction ID
     * @return array|false API response or false on failure
     */
    public function getTransaction($transactionId)
    {
        if (empty($transactionId)) {
            throw new Exception("Transaction ID is required");
        }

        $endpoint = '/transaction/' . urlencode($transactionId);
        
        return $this->makeApiRequest('GET', $endpoint);
    }

    /**
     * Make API request to Lahza
     *
     * @param string $method HTTP method (GET, POST, PUT, DELETE)
     * @param string $endpoint API endpoint
     * @param array $data Request data (for POST/PUT requests)
     * @return array|false API response or false on failure
     */
    private function makeApiRequest($method, $endpoint, $data = null)
    {
        $url = $this->apiBaseUrl . $endpoint;
        
        // Log the request details for debugging
        error_log("Lahza API Request: {$method} {$url}");
        error_log("Lahza API Base URL: {$this->apiBaseUrl}");
        error_log("Lahza API Test Mode: " . ($this->testMode ? 'Yes' : 'No'));
        if ($data) {
            error_log("Lahza API Data: " . json_encode($data));
        }
        
        // Test DNS resolution first
        $host = parse_url($url, PHP_URL_HOST);
        $ip = gethostbyname($host);
        if ($ip === $host) {
            error_log("Lahza API DNS Resolution Failed for: {$host}");
            throw new Exception("Cannot resolve hostname: {$host}. Please check your internet connection and DNS settings.");
        }
        error_log("Lahza API DNS Resolution Success: {$host} -> {$ip}");

        $ch = curl_init();

        // Set basic cURL options
        curl_setopt_array($ch, $this->curlOptions);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));
        
        // Add data for POST/PUT requests
        if ($data && in_array(strtoupper($method), array('POST', 'PUT'))) {
            $jsonData = json_encode($data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            
            // Update content length header
            $headers = $this->curlOptions[CURLOPT_HTTPHEADER];
            $headers[] = 'Content-Length: ' . strlen($jsonData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }

        // Execute request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        
        curl_close($ch);

        // Log response details with more information
        error_log("Lahza API Response Code: {$httpCode}");
        error_log("Lahza API Response Length: " . strlen($response));
        error_log("Lahza API Response: " . substr($response, 0, 1000)); // Log first 1000 chars

        // Log additional debug info
        error_log("Lahza API Request Info: " . json_encode(array(
            'url' => $url,
            'method' => $method,
            'http_code' => $httpCode,
            'total_time' => $info['total_time'] ?? 'N/A',
            'connect_time' => $info['connect_time'] ?? 'N/A'
        )));

        // Check for cURL errors
        if ($error) {
            error_log("Lahza API cURL Error: {$error}");
            error_log("Lahza API cURL Info: " . json_encode($info));
            throw new Exception("cURL Error: {$error}");
        }

        // Check for HTTP errors with more detailed error handling
        if ($httpCode >= 400) {
            $errorMessage = "HTTP Error {$httpCode}";
            $errorDetails = array();

            if ($response) {
                $decodedResponse = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    // Try different error message formats
                    if (isset($decodedResponse['message'])) {
                        $errorMessage .= ": " . $decodedResponse['message'];
                        $errorDetails['api_message'] = $decodedResponse['message'];
                    } elseif (isset($decodedResponse['error'])) {
                        $errorMessage .= ": " . $decodedResponse['error'];
                        $errorDetails['api_error'] = $decodedResponse['error'];
                    } elseif (isset($decodedResponse['errors']) && is_array($decodedResponse['errors'])) {
                        $errorMessage .= ": " . implode(', ', $decodedResponse['errors']);
                        $errorDetails['api_errors'] = $decodedResponse['errors'];
                    } elseif (isset($decodedResponse['data']['message'])) {
                        $errorMessage .= ": " . $decodedResponse['data']['message'];
                        $errorDetails['api_data_message'] = $decodedResponse['data']['message'];
                    }
                    $errorDetails['full_response'] = $decodedResponse;
                } else {
                    $errorDetails['raw_response'] = $response;
                    $errorDetails['json_error'] = json_last_error_msg();
                }
            }

            error_log("Lahza API HTTP Error Details: " . json_encode($errorDetails));
            throw new Exception($errorMessage);
        }

        // Handle empty response
        if (empty($response)) {
            error_log("Lahza API Empty Response");
            throw new Exception("Empty response from API");
        }

        // Decode JSON response
        $decodedResponse = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Lahza API JSON Error: " . json_last_error_msg());
            error_log("Lahza API Raw Response: " . $response);
            throw new Exception("Invalid JSON response from API: " . json_last_error_msg());
        }

        return $decodedResponse;
    }

    /**
     * Sanitize transaction data
     *
     * @param array $data Raw transaction data
     * @return array Sanitized transaction data
     */
    private function sanitizeTransactionData($data)
    {
        $sanitized = array();

        // Required fields
        $sanitized['email'] = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
        $sanitized['mobile'] = preg_replace('/[^0-9+]/', '', $data['mobile']);
        $sanitized['amount'] = (int) $data['amount'];

        // Validate email
        if (!filter_var($sanitized['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Invalid email address");
        }

        // Validate amount
        if ($sanitized['amount'] <= 0) {
            throw new Exception("Amount must be greater than zero");
        }

        // Optional fields
        if (isset($data['currency'])) {
            $sanitized['currency'] = strtoupper(preg_replace('/[^A-Z]/', '', $data['currency']));
        }

        if (isset($data['reference'])) {
            // Support clear WHMCS ID format: INV-10-20250618012542-abc123
            if (is_numeric($data['reference'])) {
                // Pure numeric reference (invoice ID)
                $sanitized['reference'] = (string)$data['reference'];
            } elseif (preg_match('/^INV-\d+-\d+-[a-f0-9]+$/', $data['reference'])) {
                // Clear format: INV-10-20250618012542-abc123
                $sanitized['reference'] = $data['reference'];
            } else {
                // Legacy format or other - sanitize
                $sanitized['reference'] = preg_replace('/[^a-zA-Z0-9_.-]/', '', $data['reference']);
            }
        }

        if (isset($data['callback_url'])) {
            $sanitized['callback_url'] = filter_var($data['callback_url'], FILTER_SANITIZE_URL);
        }

        if (isset($data['first_name'])) {
            $sanitized['first_name'] = htmlspecialchars(trim($data['first_name']), ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['last_name'])) {
            $sanitized['last_name'] = htmlspecialchars(trim($data['last_name']), ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['channels']) && is_array($data['channels'])) {
            $allowedChannels = array('card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer');
            $sanitized['channels'] = array_intersect($data['channels'], $allowedChannels);
        }

        if (isset($data['metadata']) && is_array($data['metadata'])) {
            $sanitized['metadata'] = $this->sanitizeMetadata($data['metadata']);
        }

        // Handle customer data structure
        if (isset($data['customer']) && is_array($data['customer'])) {
            $sanitized['customer'] = $this->sanitizeCustomerData($data['customer']);
        }

        // Handle invoice data structure
        if (isset($data['invoice']) && is_array($data['invoice'])) {
            $sanitized['invoice'] = $this->sanitizeInvoiceData($data['invoice']);
        }

        return $sanitized;
    }

    /**
     * Sanitize refund data
     *
     * @param array $data Raw refund data
     * @return array Sanitized refund data
     */
    private function sanitizeRefundData($data)
    {
        $sanitized = array();

        // Required fields
        $sanitized['transaction'] = preg_replace('/[^a-zA-Z0-9_.-]/', '', $data['transaction']);
        $sanitized['amount'] = (int) $data['amount'];

        // Validate amount
        if ($sanitized['amount'] <= 0) {
            throw new Exception("Refund amount must be greater than zero");
        }

        // Optional fields
        if (isset($data['reason'])) {
            $sanitized['reason'] = htmlspecialchars(trim($data['reason']), ENT_QUOTES, 'UTF-8');
        }

        return $sanitized;
    }

    /**
     * Sanitize customer data
     *
     * @param array $customerData Raw customer data
     * @return array Sanitized customer data
     */
    private function sanitizeCustomerData($customerData)
    {
        $sanitized = array();

        // Customer ID
        if (isset($customerData['id'])) {
            $sanitized['id'] = (string)$customerData['id'];
        }

        // Customer name
        if (isset($customerData['name'])) {
            $sanitized['name'] = htmlspecialchars(trim($customerData['name']), ENT_QUOTES, 'UTF-8');
        }

        // Email
        if (isset($customerData['email'])) {
            $sanitized['email'] = filter_var($customerData['email'], FILTER_SANITIZE_EMAIL);
        }

        // Phone
        if (isset($customerData['phone'])) {
            $sanitized['phone'] = preg_replace('/[^0-9+]/', '', $customerData['phone']);
        }

        // First name
        if (isset($customerData['first_name'])) {
            $sanitized['first_name'] = htmlspecialchars(trim($customerData['first_name']), ENT_QUOTES, 'UTF-8');
        }

        // Last name
        if (isset($customerData['last_name'])) {
            $sanitized['last_name'] = htmlspecialchars(trim($customerData['last_name']), ENT_QUOTES, 'UTF-8');
        }

        // Address
        if (isset($customerData['address']) && is_array($customerData['address'])) {
            $sanitized['address'] = array();
            
            if (isset($customerData['address']['line1'])) {
                $sanitized['address']['line1'] = htmlspecialchars(trim($customerData['address']['line1']), ENT_QUOTES, 'UTF-8');
            }
            if (isset($customerData['address']['line2'])) {
                $sanitized['address']['line2'] = htmlspecialchars(trim($customerData['address']['line2']), ENT_QUOTES, 'UTF-8');
            }
            if (isset($customerData['address']['city'])) {
                $sanitized['address']['city'] = htmlspecialchars(trim($customerData['address']['city']), ENT_QUOTES, 'UTF-8');
            }
            if (isset($customerData['address']['state'])) {
                $sanitized['address']['state'] = htmlspecialchars(trim($customerData['address']['state']), ENT_QUOTES, 'UTF-8');
            }
            if (isset($customerData['address']['postal_code'])) {
                $sanitized['address']['postal_code'] = htmlspecialchars(trim($customerData['address']['postal_code']), ENT_QUOTES, 'UTF-8');
            }
            if (isset($customerData['address']['country'])) {
                $sanitized['address']['country'] = htmlspecialchars(trim($customerData['address']['country']), ENT_QUOTES, 'UTF-8');
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize invoice data
     *
     * @param array $invoiceData Raw invoice data
     * @return array Sanitized invoice data
     */
    private function sanitizeInvoiceData($invoiceData)
    {
        $sanitized = array();

        // Invoice ID
        if (isset($invoiceData['id'])) {
            $sanitized['id'] = (string)$invoiceData['id'];
        }

        // Amount
        if (isset($invoiceData['amount'])) {
            $sanitized['amount'] = (float)$invoiceData['amount'];
        }

        // Currency
        if (isset($invoiceData['currency'])) {
            $sanitized['currency'] = strtoupper(preg_replace('/[^A-Z]/', '', $invoiceData['currency']));
        }

        // Description
        if (isset($invoiceData['description'])) {
            $sanitized['description'] = htmlspecialchars(trim($invoiceData['description']), ENT_QUOTES, 'UTF-8');
        }

        return $sanitized;
    }

    /**
     * Sanitize metadata
     *
     * @param array $metadata Raw metadata
     * @return array Sanitized metadata
     */
    private function sanitizeMetadata($metadata)
    {
        $sanitized = array();

        foreach ($metadata as $key => $value) {
            // Sanitize key
            $cleanKey = preg_replace('/[^a-zA-Z0-9_]/', '', $key);

            if (!empty($cleanKey)) {
                // Special handling for WHMCS IDs to ensure they remain clear
                if (in_array($cleanKey, array('invoiceid', 'clientid', 'invoice_id', 'client_id'))) {
                    // Keep WHMCS IDs as clear numbers
                    $sanitized[$cleanKey] = (string)$value;
                } elseif (is_string($value)) {
                    $sanitized[$cleanKey] = htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
                } elseif (is_numeric($value)) {
                    $sanitized[$cleanKey] = $value;
                } elseif (is_bool($value)) {
                    $sanitized[$cleanKey] = $value;
                }
            }
        }

        return $sanitized;
    }

    /**
     * Validate webhook signature
     *
     * @param string $payload Webhook payload
     * @param string $signature Received signature
     * @return bool True if signature is valid
     */
    public function validateWebhookSignature($payload, $signature)
    {
        $expectedSignature = hash_hmac('sha256', $payload, $this->secretKey);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get supported currencies
     *
     * @return array List of supported currencies
     */
    public function getSupportedCurrencies()
    {
        return array('ILS', 'USD', 'JOD');
    }

    /**
     * Get supported payment channels
     *
     * @return array List of supported payment channels
     */
    public function getSupportedChannels()
    {
        return array('card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer');
    }

    /**
     * Format amount to lowest currency unit
     *
     * @param float $amount Amount in main currency unit
     * @return int Amount in lowest currency unit (cents)
     */
    public static function formatAmount($amount)
    {
        return (int) round($amount * 100);
    }

    /**
     * Format amount from lowest currency unit
     *
     * @param int $amount Amount in lowest currency unit (cents)
     * @return float Amount in main currency unit
     */
    public static function parseAmount($amount)
    {
        return $amount / 100;
    }
}