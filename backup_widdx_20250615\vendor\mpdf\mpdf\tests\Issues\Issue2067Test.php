<?php

namespace Issues;

class Issue2067Test extends \Mpdf\BaseMpdfTest
{

	public function testSvgViewboxCommas()
	{
		$mpdf = new \Mpdf\Mpdf();

		$mpdf->WriteHTML('<?xml version="1.0" encoding="UTF-8"?>
<svg width="45" height="45" viewBox="0, 0, 45, 45" xmlns="http://www.w3.org/2000/svg">
    <g id="Group">
        <path id="Path" fill="#eb003c" stroke="none" d="M 55.98 30.695 L 55.98 27.104 L 58.908001 28.4 C 60.455002 28.4 61.278 27.565001 61.278 26.002001 L 61.278 13.767 L 65.365997 13.767 L 65.365997 26.177999 C 65.365997 28.112 64.844002 29.504999 63.800999 30.354 C 62.758999 31.204 61.472 31.628 59.942001 31.628 C 58.630001 31.628 57.307999 31.316999 55.98 30.695 M 67.827003 22.518999 C 67.827003 16.254 70.670998 13.41 75.426003 13.41 C 80.684998 13.41 83.151001 16.403999 83.151001 22.518999 C 83.151001 29.062 80.258003 31.628 75.527 31.628 C 70.519997 31.628 67.827003 28.76 67.827003 22.518999 M 78.902 22.518999 C 78.902 18.398001 77.794998 16.698999 75.425003 16.698999 C 73.184998 16.698999 72.051003 18.475 72.051003 22.518999 C 72.051003 26.563999 73.313004 28.339001 75.580002 28.339001 C 77.794998 28.339001 78.902 26.666 78.902 22.518999 M 85.621002 13.76 L 89.672997 13.76 L 89.672997 20.851 L 95.107002 20.851 L 95.107002 13.76 L 99.183998 13.76 L 99.183998 31.278999 L 95.107002 31.278999 L 95.107002 24.318001 L 89.672997 24.318001 L 89.672997 31.278999 L 85.621002 31.278999 Z M 116.143997 31.278 L 110.733002 13.76 L 106.431 13.76 L 100.862999 31.278 L 104.580002 31.278 L 105.558998 27.888 L 111.099998 27.888 L 112.093002 31.278 Z M 110.182999 24.761 L 106.461998 24.761 L 108.307999 18.363001 Z M 148.087997 13.76 L 148.087997 31.278999 L 144.744995 31.278999 L 138.602997 20.615 L 138.602997 31.278999 L 134.550003 31.278999 L 134.550003 13.76 L 138.313995 13.76 L 144.037003 24.187 L 144.037003 13.76 Z M 131.360001 13.76 L 131.360001 31.278999 L 128.020004 31.278999 L 121.875999 20.615 L 121.875999 31.278999 L 117.822998 31.278999 L 117.822998 13.76 L 121.586998 13.76 L 127.308998 24.187 L 127.308998 13.76 Z M 151.283005 13.76 L 155.332001 13.76 L 155.332001 31.278999 L 151.283005 31.278999 Z M 161.945999 16.834999 L 157.347 16.834999 L 157.347 13.76 L 170.701004 13.76 L 170.701004 16.834999 L 166.022995 16.834999 L 166.022995 31.278 L 161.945999 31.278 Z M 195.839996 24.306999 C 196.919006 23.958 197.817001 23.392 198.444 22.423 C 199.074005 21.452 199.350006 20.438999 199.350006 19.302 C 199.350006 17.466 198.796997 16.084 197.697006 15.157 C 196.593002 14.23 195.039001 13.76 193.029007 13.76 L 186.009995 13.76 L 186.009995 31.278 L 190.035995 31.278 L 190.035995 24.836 L 191.813995 24.836 L 195.134995 31.278999 L 199.979996 31.278999 Z M 192.787994 21.660999 L 190.035995 21.660999 L 190.035995 16.837 L 192.970993 16.837 C 193.578003 16.837 194.235001 16.996 194.651001 17.412001 L 195.229004 19.190001 C 195.229004 20.771 194.414993 21.660999 192.787994 21.660999 M 172.667007 31.278999 L 182.973007 31.278999 L 182.973007 28.200001 L 176.718002 28.200001 L 176.718002 24.058001 L 181.949005 24.058001 L 181.949005 20.952999 L 176.718002 20.952999 L 176.718002 16.837 L 182.973007 16.837 L 182.973007 13.76 L 172.667007 13.76 Z M 44.98 22.499001 C 44.98 34.914001 34.915001 44.979 22.5 44.979 C 10.085 44.979 0.02 34.914001 0.02 22.499001 C 0.02 10.084 10.085 0.019001 22.5 0.019001 C 34.915001 0.019001 44.98 10.084 44.98 22.499001"/>
    </g>
</svg>');

		$this->mpdf->Output('', 'S');
	}

}
