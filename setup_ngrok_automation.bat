@echo off
echo ========================================
echo    Lahza Payment Gateway - ngrok Setup
echo ========================================
echo.

REM Check if ngrok is installed
where ngrok >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] ngrok is not installed or not in PATH
    echo.
    echo Please download ngrok from: https://ngrok.com/download
    echo Or install via Chocolatey: choco install ngrok
    echo.
    pause
    exit /b 1
)

echo [INFO] ngrok found in system PATH
echo.

REM Check if XAMPP is running
echo [INFO] Checking if XAMPP Apache is running...
netstat -an | find "80" | find "LISTENING" >nul
if %errorlevel% neq 0 (
    echo [WARNING] Port 80 is not listening. Please start XAMPP Apache first.
    echo.
    echo Starting XAMPP Control Panel...
    start "" "C:\xampp\xampp-control.exe"
    echo.
    echo Please start Apache and MySQL, then press any key to continue...
    pause
)

echo [INFO] Port 80 is available
echo.

REM Create ngrok configuration
echo [INFO] Creating ngrok configuration...
echo version: "2" > ngrok.yml
echo authtoken: YOUR_AUTH_TOKEN_HERE >> ngrok.yml
echo tunnels: >> ngrok.yml
echo   whmcs: >> ngrok.yml
echo     addr: 80 >> ngrok.yml
echo     proto: http >> ngrok.yml
echo     bind_tls: true >> ngrok.yml

echo [INFO] ngrok configuration created
echo.

REM Instructions for user
echo ========================================
echo    SETUP INSTRUCTIONS
echo ========================================
echo.
echo 1. Get your ngrok auth token:
echo    - Go to: https://dashboard.ngrok.com/get-started/your-authtoken
echo    - Copy your auth token
echo.
echo 2. Add your auth token:
set /p authtoken="   Enter your ngrok auth token: "

if "%authtoken%"=="" (
    echo [ERROR] Auth token is required
    pause
    exit /b 1
)

echo [INFO] Adding auth token to ngrok...
ngrok config add-authtoken %authtoken%

if %errorlevel% neq 0 (
    echo [ERROR] Failed to add auth token
    pause
    exit /b 1
)

echo [SUCCESS] Auth token added successfully
echo.

REM Start ngrok
echo [INFO] Starting ngrok tunnel...
echo.
echo ========================================
echo    NGROK TUNNEL STARTING
echo ========================================
echo.
echo Keep this window open while testing!
echo The tunnel will be available at a URL like: https://abc123.ngrok.io
echo.
echo Press Ctrl+C to stop the tunnel when done.
echo.

REM Start ngrok and capture the URL
start "ngrok" ngrok http 80

REM Wait a moment for ngrok to start
timeout /t 5 /nobreak >nul

REM Try to get the ngrok URL
echo [INFO] Attempting to get ngrok URL...
curl -s http://localhost:4040/api/tunnels > ngrok_info.json 2>nul

if exist ngrok_info.json (
    echo [INFO] ngrok API response saved to ngrok_info.json
    echo.
    echo ========================================
    echo    NEXT STEPS
    echo ========================================
    echo.
    echo 1. Open ngrok interface: http://localhost:4040
    echo 2. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
    echo 3. Update Lahza webhook URL to:
    echo    https://YOUR-NGROK-URL.ngrok.io/Whmcs/modules/gateways/callback/lahza.php
    echo.
    echo 4. Test the payment gateway
    echo.
) else (
    echo [WARNING] Could not automatically detect ngrok URL
    echo Please check http://localhost:4040 for the tunnel URL
)

echo Press any key to open ngrok interface...
pause >nul
start http://localhost:4040

echo.
echo ========================================
echo    TUNNEL IS RUNNING
echo ========================================
echo.
echo Keep this window open while testing!
echo Close this window or press Ctrl+C to stop the tunnel.
echo.

REM Keep the script running
:loop
timeout /t 30 /nobreak >nul
goto loop
