# دليل حل مشاكل Webhook - Webhook Troubleshooting Guide

## 🚨 المشكلة الحالية | Current Issue
الفاتورة #13 لا تتحدث إلى "Paid" بعد نجاح الدفع، مما يشير إلى أن webhook لم يصل أو لم يتم معالجته بشكل صحيح.

## 🔍 الأسباب المحتملة | Possible Causes

### 1. مشكلة في إعدادات Webhook في لحظة
- **المشكلة**: Webhook URL غير محدد أو خاطئ في لوحة تحكم لحظة
- **الحل**: تحقق من إعدادات Webhook في لوحة تحكم لحظة

### 2. مشكلة في الوصول إلى Webhook URL
- **المشكلة**: localhost غير قابل للوصول من الخارج
- **الحل**: استخدم ngrok أو خدمة مشابهة لتوفير URL عام

### 3. مشكلة في معالجة Webhook
- **المشكلة**: خطأ في كود معالجة webhook
- **الحل**: فحص السجلات والاختبار اليدوي

## 🛠️ خطوات الحل | Solution Steps

### الخطوة 1: فحص إعدادات لحظة
1. سجل الدخول إلى لوحة تحكم لحظة
2. انتقل إلى إعدادات Webhook
3. تأكد من أن URL محدد كالتالي:
   ```
   http://your-domain.com/Whmcs/modules/gateways/callback/lahza.php
   ```
4. تأكد من تفعيل الأحداث التالية:
   - `charge.success`
   - `charge.failed`

### الخطوة 2: حل مشكلة localhost
نظراً لأن لحظة لا يمكنها الوصول إلى `localhost`، تحتاج إلى:

#### الحل أ: استخدام ngrok
```bash
# تحميل وتثبيت ngrok
ngrok http 80

# سيعطيك URL مثل: https://abc123.ngrok.io
# استخدم هذا URL في إعدادات لحظة:
# https://abc123.ngrok.io/Whmcs/modules/gateways/callback/lahza.php
```

#### الحل ب: استخدام خادم حقيقي
- رفع WHMCS إلى خادم حقيقي
- استخدام domain حقيقي

### الخطوة 3: اختبار Webhook يدوياً
استخدم الملفات التالية للاختبار:
- `test_webhook_url.php` - اختبار وصول webhook
- `manual_webhook_test.php` - محاكاة webhook
- `debug_payment_issue.php` - فحص السجلات

### الخطوة 4: إضافة الدفع يدوياً (حل مؤقت)
إذا لم يعمل webhook، يمكنك إضافة الدفع يدوياً:
```php
// استخدم add_payment_direct.php
// أو من WHMCS Admin:
// Billing → View Invoice → Add Payment
```

## 🧪 اختبار النظام | System Testing

### اختبار 1: فحص وصول Webhook
```bash
curl -X POST http://localhost/Whmcs/modules/gateways/callback/lahza.php \
  -H "Content-Type: application/json" \
  -d '{"event":"charge.success","data":{"id":"test","reference":"INV-13-test","status":"success","amount":11000,"metadata":{"invoiceid":"13"}}}'
```

### اختبار 2: فحص السجلات
- Gateway Logs: `Admin → Logs → Gateway Log`
- Module Logs: `Admin → Logs → Module Log`
- Activity Logs: `Admin → Logs → Activity Log`

### اختبار 3: فحص قاعدة البيانات
```sql
-- فحص حالة الفاتورة
SELECT id, status, total FROM tblinvoices WHERE id = 13;

-- فحص سجلات الدفع
SELECT * FROM tblaccounts WHERE invoiceid = 13;

-- فحص سجلات Gateway
SELECT * FROM tblgatewaylog WHERE gateway = 'lahza' ORDER BY date DESC LIMIT 5;
```

## 🔧 الحلول السريعة | Quick Fixes

### الحل 1: إعادة تعيين حالة الفاتورة يدوياً
```php
// في WHMCS Admin
// Billing → View Invoice → Mark as Paid
```

### الحل 2: إضافة الدفع عبر API
```php
$result = addInvoicePayment(
    13,                    // Invoice ID
    'txn_manual_' . time(), // Transaction ID
    110.00,                // Amount
    3.30,                  // Fee
    'lahza'               // Gateway
);
```

### الحل 3: تحديث قاعدة البيانات مباشرة
```sql
-- تحديث حالة الفاتورة (استخدم بحذر)
UPDATE tblinvoices SET status = 'Paid' WHERE id = 13;

-- إضافة سجل دفع
INSERT INTO tblaccounts (invoiceid, transid, amountin, fees, gateway, date) 
VALUES (13, 'manual_payment', 110.00, 3.30, 'lahza', NOW());
```

## 📋 قائمة التحقق | Checklist

- [ ] Webhook URL محدد في لوحة تحكم لحظة
- [ ] URL قابل للوصول من الخارج (ليس localhost)
- [ ] أحداث webhook مفعلة (charge.success, charge.failed)
- [ ] بوابة لحظة مفعلة في WHMCS
- [ ] API keys صحيحة
- [ ] وضع الاختبار مفعل
- [ ] السجلات مفعلة
- [ ] لا توجد أخطاء في السجلات

## 🆘 الدعم الطارئ | Emergency Support

إذا كنت بحاجة لحل سريع:

1. **إضافة الدفع يدوياً**:
   - `Admin → Billing → View Invoice #13 → Add Payment`

2. **تحديث الحالة مباشرة**:
   - `Admin → Billing → View Invoice #13 → Mark as Paid`

3. **استخدام الملفات المساعدة**:
   - `manual_webhook_test.php`
   - `add_payment_direct.php`

## 📞 الخطوات التالية | Next Steps

1. **فحص لوحة تحكم لحظة** للتأكد من إرسال webhook
2. **إعداد ngrok** لحل مشكلة localhost
3. **اختبار webhook** باستخدام الملفات المساعدة
4. **فحص السجلات** للعثور على الأخطاء
5. **إضافة الدفع يدوياً** كحل مؤقت

---

**ملاحظة مهمة**: المشكلة الأساسية هي أن لحظة لا يمكنها الوصول إلى `localhost`. استخدم ngrok أو خادم حقيقي لحل هذه المشكلة.
