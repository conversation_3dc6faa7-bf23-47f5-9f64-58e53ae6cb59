<?php
/**
 * Final Test Suite for Lahza Payment Gateway
 */

require_once __DIR__ . '/init.php';

echo "<h1>🧪 Final Test Suite - Lahza Payment Gateway</h1>";

// Auto-detect ngrok URL
function getNgrokUrl() {
    try {
        $response = @file_get_contents('http://localhost:4040/api/tunnels');
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['tunnels'])) {
                foreach ($data['tunnels'] as $tunnel) {
                    if ($tunnel['proto'] === 'https') {
                        return $tunnel['public_url'];
                    }
                }
            }
        }
    } catch (Exception $e) {
        // Ignore errors
    }
    return 'https://your-ngrok-url.ngrok.io'; // Fallback
}

// Test configurations
$testConfigs = [
    'ngrok_url' => getNgrokUrl(),
    'test_invoice_id' => 14, // Create a new test invoice
    'test_amount' => 25.00
];

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>📋 Test Configuration</h2>";
echo "<ul>";
echo "<li><strong>ngrok URL:</strong> " . htmlspecialchars($testConfigs['ngrok_url']) . "</li>";
echo "<li><strong>Test Invoice ID:</strong> " . $testConfigs['test_invoice_id'] . "</li>";
echo "<li><strong>Test Amount:</strong> $" . number_format($testConfigs['test_amount'], 2) . "</li>";
echo "</ul>";
echo "</div>";

// Test 1: Gateway Configuration
echo "<h2>🔧 Test 1: Gateway Configuration</h2>";

try {
    require_once __DIR__ . '/includes/gatewayfunctions.php';
    $gatewayParams = getGatewayVariables('lahza');
    
    if (!$gatewayParams['type']) {
        echo "<p>❌ <strong>FAIL:</strong> Lahza gateway not activated</p>";
    } else {
        echo "<p>✅ <strong>PASS:</strong> Gateway is activated</p>";
        
        $checks = [
            'publicKey' => !empty($gatewayParams['publicKey']),
            'secretKey' => !empty($gatewayParams['secretKey']),
            'testMode' => $gatewayParams['testMode'] === 'on'
        ];
        
        foreach ($checks as $key => $passed) {
            $status = $passed ? "✅ PASS" : "❌ FAIL";
            echo "<p>{$status}: {$key}</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>ERROR:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 2: Database Connection
echo "<h2>🗄️ Test 2: Database Connection</h2>";

try {
    $pdo = \WHMCS\Database\Capsule::connection()->getPdo();
    echo "<p>✅ <strong>PASS:</strong> Database connection successful</p>";
    
    // Check test invoice
    $stmt = $pdo->prepare("SELECT id, status, total, paymentmethod FROM tblinvoices WHERE id = ?");
    $stmt->execute([$testConfigs['test_invoice_id']]);
    $invoice = $stmt->fetch();
    
    if ($invoice) {
        echo "<p>✅ <strong>PASS:</strong> Test invoice exists</p>";
        echo "<ul>";
        echo "<li>Status: " . htmlspecialchars($invoice['status']) . "</li>";
        echo "<li>Total: $" . number_format($invoice['total'], 2) . "</li>";
        echo "<li>Payment Method: " . htmlspecialchars($invoice['paymentmethod']) . "</li>";
        echo "</ul>";
        
        if ($invoice['paymentmethod'] !== 'lahza') {
            echo "<p>⚠️ <strong>WARNING:</strong> Invoice payment method is not 'lahza'</p>";
        }
    } else {
        echo "<p>❌ <strong>FAIL:</strong> Test invoice not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>ERROR:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 3: Callback URL Accessibility
echo "<h2>🌐 Test 3: Callback URL Accessibility</h2>";

$callbackUrl = $testConfigs['ngrok_url'] . '/Whmcs/modules/gateways/callback/lahza.php';

echo "<p><strong>Testing URL:</strong> <code>" . htmlspecialchars($callbackUrl) . "</code></p>";

// Test GET request
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $callbackUrl . '?reference=INV-' . $testConfigs['test_invoice_id'] . '-test');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "<p>❌ <strong>FAIL:</strong> Cannot reach callback URL - " . htmlspecialchars($error) . "</p>";
} else {
    echo "<p>✅ <strong>PASS:</strong> Callback URL accessible (HTTP {$httpCode})</p>";
}

// Test 4: Webhook Simulation
echo "<h2>📡 Test 4: Webhook Simulation</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_webhook'])) {
    
    $webhookData = [
        'event' => 'charge.success',
        'data' => [
            'id' => 'txn_final_test_' . time(),
            'reference' => 'INV-' . $testConfigs['test_invoice_id'] . '-' . date('YmdHis') . '-test01',
            'status' => 'success',
            'amount' => $testConfigs['test_amount'] * 100, // Convert to cents
            'fees' => intval($testConfigs['test_amount'] * 100 * 0.03), // 3% fee
            'metadata' => [
                'invoiceid' => strval($testConfigs['test_invoice_id']),
                'clientid' => '1'
            ]
        ]
    ];
    
    $jsonData = json_encode($webhookData);
    
    echo "<h3>Sending Test Webhook:</h3>";
    echo "<pre>" . json_encode($webhookData, JSON_PRETTY_PRINT) . "</pre>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $callbackUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($jsonData),
        'User-Agent: Lahza-Webhook/1.0'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<h3>Webhook Response:</h3>";
    echo "<p><strong>HTTP Code:</strong> {$httpCode}</p>";
    
    if ($error) {
        echo "<p>❌ <strong>FAIL:</strong> " . htmlspecialchars($error) . "</p>";
    } else {
        echo "<p><strong>Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        if ($httpCode == 200) {
            echo "<p>✅ <strong>PASS:</strong> Webhook processed successfully</p>";
            
            // Check if invoice status changed
            $stmt = $pdo->prepare("SELECT status FROM tblinvoices WHERE id = ?");
            $stmt->execute([$testConfigs['test_invoice_id']]);
            $newStatus = $stmt->fetchColumn();
            
            echo "<p><strong>Invoice Status After Webhook:</strong> " . htmlspecialchars($newStatus) . "</p>";
            
            if ($newStatus === 'Paid') {
                echo "<p>🎉 <strong>SUCCESS:</strong> Invoice marked as paid!</p>";
            } else {
                echo "<p>❌ <strong>FAIL:</strong> Invoice not marked as paid</p>";
            }
        } else {
            echo "<p>❌ <strong>FAIL:</strong> Webhook failed with HTTP {$httpCode}</p>";
        }
    }
    
} else {
    echo "<form method='post'>";
    echo "<button type='submit' name='test_webhook' style='padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px;'>🚀 Run Webhook Test</button>";
    echo "</form>";
}

// Test 5: Gateway Logs
echo "<h2>📊 Test 5: Recent Gateway Logs</h2>";

try {
    $stmt = $pdo->prepare("
        SELECT date, action, description, data 
        FROM tblgatewaylog 
        WHERE gateway = 'lahza' 
        ORDER BY date DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $logs = $stmt->fetchAll();
    
    if ($logs) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Date</th><th>Action</th><th>Description</th><th>Data</th></tr>";
        foreach ($logs as $log) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($log['date']) . "</td>";
            echo "<td>" . htmlspecialchars($log['action']) . "</td>";
            echo "<td>" . htmlspecialchars($log['description']) . "</td>";
            echo "<td><pre style='max-width: 300px; overflow: auto; font-size: 11px;'>" . htmlspecialchars(substr($log['data'], 0, 200)) . "...</pre></td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p>✅ <strong>PASS:</strong> Gateway logs found</p>";
    } else {
        echo "<p>⚠️ <strong>WARNING:</strong> No recent gateway logs found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>ERROR:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test Summary
echo "<h2>📋 Test Summary</h2>";

echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ What Should Work:</h3>";
echo "<ul>";
echo "<li>Gateway configuration is complete</li>";
echo "<li>Callback URL is accessible via ngrok</li>";
echo "<li>Webhook processing works correctly</li>";
echo "<li>Invoice status updates to 'Paid'</li>";
echo "<li>Payment records are created</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>⚠️ Important Notes:</h3>";
echo "<ul>";
echo "<li>Make sure ngrok is running and URL is updated in Lahza dashboard</li>";
echo "<li>Use HTTPS ngrok URL (not HTTP)</li>";
echo "<li>Test with real payment flow after webhook test passes</li>";
echo "<li>Monitor ngrok interface at http://127.0.0.1:4040</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Quick Links:</h3>";
echo "<ul>";
echo "<li><a href='/Whmcs/viewinvoice.php?id=" . $testConfigs['test_invoice_id'] . "' target='_blank'>View Test Invoice</a></li>";
echo "<li><a href='/Whmcs/admin/systemgatewaylog.php' target='_blank'>Gateway Logs</a></li>";
echo "<li><a href='http://127.0.0.1:4040' target='_blank'>ngrok Interface</a></li>";
echo "<li><a href='/Whmcs/NGROK_SETUP_GUIDE.md' target='_blank'>ngrok Setup Guide</a></li>";
echo "</ul>";

echo "<style>";
echo "body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }";
echo "h1, h2, h3 { color: #333; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }";
echo "table { font-size: 12px; }";
echo "th { background: #f8f9fa; padding: 8px; }";
echo "td { padding: 6px; }";
echo "</style>";
?>
